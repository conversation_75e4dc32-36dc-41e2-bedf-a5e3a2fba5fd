<?php

namespace App\Core;

use App\Core\Database\DatabaseManager;
use App\Core\Tenant\TenantManager;
use App\Core\Config\ConfigManager;
use App\Core\Router\Router;
use App\Core\Http\Request;
use App\Core\Http\Response;

/**
 * Main Application Class
 * 
 * Handles application initialization, routing, and request processing
 */
class Application
{
    private static ?Application $instance = null;
    private DatabaseManager $database;
    private TenantManager $tenant;
    private ConfigManager $config;
    private Router $router;
    private bool $initialized = false;

    private function __construct()
    {
        // Private constructor for singleton
    }

    public static function getInstance(): Application
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Initialize the application
     */
    public function initialize(): void
    {
        if ($this->initialized) {
            return;
        }

        // Initialize core components
        $this->config = new ConfigManager();
        $this->database = new DatabaseManager($this->config);
        $this->tenant = new TenantManager($this->database);
        $this->router = new Router();

        // Initialize tenant context
        $this->tenant->initializeFromRequest();

        // Register routes
        $this->registerRoutes();

        $this->initialized = true;
    }

    /**
     * Handle HTTP request
     */
    public function handleRequest(): Response
    {
        $request = Request::createFromGlobals();
        return $this->router->dispatch($request);
    }

    /**
     * Register application routes
     */
    private function registerRoutes(): void
    {
        // API Routes
        $this->router->group('/api', function ($router) {
            // Customer routes
            $router->get('/customers', 'App\Controllers\Api\CustomerController@index');
            $router->get('/customers/search', 'App\Controllers\Api\CustomerController@search');
            $router->get('/customers/{id}', 'App\Controllers\Api\CustomerController@show');
            $router->get('/customers/{id}/reservations', 'App\Controllers\Api\CustomerController@reservations');
            $router->get('/customers/{id}/statistics', 'App\Controllers\Api\CustomerController@statistics');
            $router->post('/customers', 'App\Controllers\Api\CustomerController@store');
            $router->put('/customers/{id}', 'App\Controllers\Api\CustomerController@update');
            $router->post('/customers/{id}/update-statistics', 'App\Controllers\Api\CustomerController@updateStatistics');
            $router->delete('/customers/{id}', 'App\Controllers\Api\CustomerController@destroy');
            $router->post('/customers/verify', 'App\Controllers\Api\CustomerController@verify');

            // Reservation routes
            $router->get('/reservations', 'App\Controllers\Api\ReservationController@index');
            $router->get('/reservations/upcoming', 'App\Controllers\Api\ReservationController@upcoming');
            $router->get('/reservations/revenue', 'App\Controllers\Api\ReservationController@revenue');
            $router->get('/reservations/date/{date}', 'App\Controllers\Api\ReservationController@forDate');
            $router->get('/reservations/{id}', 'App\Controllers\Api\ReservationController@show');
            $router->post('/reservations', 'App\Controllers\Api\ReservationController@store');
            $router->put('/reservations/{id}', 'App\Controllers\Api\ReservationController@update');
            $router->post('/reservations/{id}/confirm', 'App\Controllers\Api\ReservationController@confirm');
            $router->post('/reservations/{id}/complete', 'App\Controllers\Api\ReservationController@complete');
            $router->post('/reservations/{id}/cancel', 'App\Controllers\Api\ReservationController@cancel');
            $router->post('/reservations/{id}/no-show', 'App\Controllers\Api\ReservationController@noShow');
            $router->delete('/reservations/{id}', 'App\Controllers\Api\ReservationController@destroy');

            // Service routes
            $router->get('/services', 'App\Controllers\Api\ServiceController@index');
            $router->get('/services/popular', 'App\Controllers\Api\ServiceController@popular');
            $router->get('/services/options', 'App\Controllers\Api\ServiceController@options');
            $router->get('/services/{id}', 'App\Controllers\Api\ServiceController@show');
            $router->get('/services/{id}/employees', 'App\Controllers\Api\ServiceController@employees');
            $router->get('/services/{id}/statistics', 'App\Controllers\Api\ServiceController@statistics');
            $router->post('/services', 'App\Controllers\Api\ServiceController@store');
            $router->put('/services/{id}', 'App\Controllers\Api\ServiceController@update');
            $router->post('/services/{id}/assign-employees', 'App\Controllers\Api\ServiceController@assignEmployees');
            $router->delete('/services/{id}/employees/{employee_id}', 'App\Controllers\Api\ServiceController@removeEmployee');
            $router->delete('/services/{id}', 'App\Controllers\Api\ServiceController@destroy');

            // Employee routes
            $router->get('/employees', 'App\Controllers\Api\EmployeeController@index');
            $router->get('/employees/active', 'App\Controllers\Api\EmployeeController@active');
            $router->get('/employees/service/{service_id}', 'App\Controllers\Api\EmployeeController@forService');
            $router->get('/employees/{id}', 'App\Controllers\Api\EmployeeController@show');
            $router->get('/employees/{id}/services', 'App\Controllers\Api\EmployeeController@services');
            $router->get('/employees/{id}/working-hours', 'App\Controllers\Api\EmployeeController@workingHours');
            $router->get('/employees/{id}/availability', 'App\Controllers\Api\EmployeeController@availability');
            $router->get('/employees/{id}/reservations', 'App\Controllers\Api\EmployeeController@reservations');
            $router->get('/employees/{id}/statistics', 'App\Controllers\Api\EmployeeController@statistics');
            $router->post('/employees', 'App\Controllers\Api\EmployeeController@store');
            $router->put('/employees/{id}', 'App\Controllers\Api\EmployeeController@update');
            $router->put('/employees/{id}/working-hours', 'App\Controllers\Api\EmployeeController@updateWorkingHours');
            $router->post('/employees/{id}/activate', 'App\Controllers\Api\EmployeeController@activate');
            $router->post('/employees/{id}/deactivate', 'App\Controllers\Api\EmployeeController@deactivate');
            $router->post('/employees/{id}/assign-services', 'App\Controllers\Api\EmployeeController@assignServices');
            $router->delete('/employees/{id}/services/{service_id}', 'App\Controllers\Api\EmployeeController@removeService');
            $router->delete('/employees/{id}', 'App\Controllers\Api\EmployeeController@destroy');

            // Legacy routes for compatibility
            $router->get('/availability/{date}/{service}', 'App\Controllers\Api\AvailabilityController@getTimeSlots');
        });

        // Admin Routes
        $this->router->group('/admin', function ($router) {
            $router->get('/login', 'App\Controllers\Admin\AuthController@showLogin');
            $router->post('/login', 'App\Controllers\Admin\AuthController@login');
            $router->post('/logout', 'App\Controllers\Admin\AuthController@logout');
            $router->get('/check', 'App\Controllers\Admin\AuthController@check');

            // Protected admin routes
            $router->middleware(['auth'], function ($router) {
                $router->get('/', 'App\Controllers\Admin\DashboardController@index');
                $router->get('/dashboard', 'App\Controllers\Admin\DashboardController@index');
                $router->get('/dashboard/stats', 'App\Controllers\Admin\DashboardController@stats');
                $router->get('/dashboard/revenue-chart', 'App\Controllers\Admin\DashboardController@revenueChart');
                $router->get('/dashboard/bookings-chart', 'App\Controllers\Admin\DashboardController@bookingsChart');
                $router->post('/change-password', 'App\Controllers\Admin\AuthController@changePassword');
                $router->get('/session-info', 'App\Controllers\Admin\AuthController@sessionInfo');
                $router->post('/extend-session', 'App\Controllers\Admin\AuthController@extendSession');

                // Legacy admin routes
                $router->get('/reservations', 'App\Controllers\Admin\ReservationController@index');
                $router->get('/customers', 'App\Controllers\Admin\CustomerController@index');
                $router->get('/employees', 'App\Controllers\Admin\EmployeeController@index');
                $router->get('/services', 'App\Controllers\Admin\ServiceController@index');
                $router->get('/settings', 'App\Controllers\Admin\SettingsController@index');
            });
        });

        // Test route
        $this->router->get('/test', function ($request, $params) {
            return Response::json(['status' => 'OK', 'message' => 'Application is working!']);
        });

        // Frontend Routes
        $this->router->get('/', 'App\Controllers\Frontend\BookingController@index');
        $this->router->get('/book', 'App\Controllers\Frontend\BookingController@index');
        $this->router->get('/services', 'App\Controllers\Frontend\BookingController@services');
        $this->router->get('/services/{service}/employees', 'App\Controllers\Frontend\BookingController@employeesForService');
        $this->router->get('/available-slots', 'App\Controllers\Frontend\BookingController@availableSlots');
        $this->router->post('/book', 'App\Controllers\Frontend\BookingController@book');
        $this->router->get('/booking/{id}', 'App\Controllers\Frontend\BookingController@details');
        $this->router->post('/booking/{id}/cancel', 'App\Controllers\Frontend\BookingController@cancel');

        // Legacy frontend routes
        $this->router->post('/book', 'App\Controllers\Frontend\BookingController@store');
        $this->router->get('/verify', 'App\Controllers\Frontend\BookingController@verify');
    }

    /**
     * Get database manager
     */
    public function getDatabase(): DatabaseManager
    {
        return $this->database;
    }

    /**
     * Get tenant manager
     */
    public function getTenant(): TenantManager
    {
        return $this->tenant;
    }

    /**
     * Get config manager
     */
    public function getConfig(): ConfigManager
    {
        return $this->config;
    }

    /**
     * Get router
     */
    public function getRouter(): Router
    {
        return $this->router;
    }

    /**
     * Run the application
     */
    public function run(): void
    {
        try {
            // Create request object
            $request = Request::createFromGlobals();

            // Route the request
            $response = $this->router->dispatch($request);

            // Send response
            $response->send();
        } catch (\Exception $e) {
            // Handle routing errors
            $response = Response::json([
                'error' => 'Application Error',
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ], 500);

            $response->send();
        }
    }
}
