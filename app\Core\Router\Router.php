<?php

namespace App\Core\Router;

use App\Core\Http\Request;
use App\Core\Http\Response;

/**
 * Router Class
 * 
 * Handles routing and dispatching
 */
class Router
{
    private array $routes = [];
    private array $middleware = [];
    private array $groupStack = [];

    /**
     * Add GET route
     */
    public function get(string $path, $handler): void
    {
        $this->addRoute('GET', $path, $handler);
    }

    /**
     * Add POST route
     */
    public function post(string $path, $handler): void
    {
        $this->addRoute('POST', $path, $handler);
    }

    /**
     * Add PUT route
     */
    public function put(string $path, $handler): void
    {
        $this->addRoute('PUT', $path, $handler);
    }

    /**
     * Add DELETE route
     */
    public function delete(string $path, $handler): void
    {
        $this->addRoute('DELETE', $path, $handler);
    }

    /**
     * Add route with any method
     */
    public function any(string $path, $handler): void
    {
        $methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'];
        foreach ($methods as $method) {
            $this->addRoute($method, $path, $handler);
        }
    }

    /**
     * Add route group
     */
    public function group(string $prefix, callable $callback): void
    {
        $this->groupStack[] = ['prefix' => $prefix];
        $callback($this);
        array_pop($this->groupStack);
    }

    /**
     * Add middleware to routes
     */
    public function middleware(array $middleware, callable $callback): void
    {
        $this->groupStack[] = ['middleware' => $middleware];
        $callback($this);
        array_pop($this->groupStack);
    }

    /**
     * Add route
     */
    private function addRoute(string $method, string $path, $handler): void
    {
        // Apply group prefixes
        $fullPath = $this->getFullPath($path);
        
        // Apply group middleware
        $middleware = $this->getGroupMiddleware();

        $this->routes[] = [
            'method' => $method,
            'path' => $fullPath,
            'handler' => $handler,
            'middleware' => $middleware,
            'pattern' => $this->convertToPattern($fullPath)
        ];
    }

    /**
     * Get full path with group prefixes
     */
    private function getFullPath(string $path): string
    {
        $prefixes = [];
        foreach ($this->groupStack as $group) {
            if (isset($group['prefix'])) {
                $prefixes[] = trim($group['prefix'], '/');
            }
        }

        $fullPath = '/' . implode('/', $prefixes) . '/' . trim($path, '/');
        return rtrim($fullPath, '/') ?: '/';
    }

    /**
     * Get group middleware
     */
    private function getGroupMiddleware(): array
    {
        $middleware = [];
        foreach ($this->groupStack as $group) {
            if (isset($group['middleware'])) {
                $middleware = array_merge($middleware, $group['middleware']);
            }
        }
        return $middleware;
    }

    /**
     * Convert path to regex pattern
     */
    private function convertToPattern(string $path): string
    {
        // Convert {param} to named capture groups
        $pattern = preg_replace('/\{([^}]+)\}/', '(?P<$1>[^/]+)', $path);
        return '#^' . $pattern . '$#';
    }

    /**
     * Dispatch request
     */
    public function dispatch(Request $request): Response
    {
        $method = $request->getMethod();
        $path = $request->getPath();

        foreach ($this->routes as $route) {
            if ($route['method'] === $method && preg_match($route['pattern'], $path, $matches)) {
                // Extract parameters
                $params = array_filter($matches, 'is_string', ARRAY_FILTER_USE_KEY);
                
                // Apply middleware
                foreach ($route['middleware'] as $middlewareName) {
                    $middlewareResult = $this->applyMiddleware($middlewareName, $request);
                    if ($middlewareResult instanceof Response) {
                        return $middlewareResult;
                    }
                }

                // Call handler
                return $this->callHandler($route['handler'], $request, $params);
            }
        }

        return Response::notFound();
    }

    /**
     * Apply middleware
     */
    private function applyMiddleware(string $middlewareName, Request $request): ?Response
    {
        switch ($middlewareName) {
            case 'auth':
                return $this->authMiddleware($request);
            case 'tenant':
                return $this->tenantMiddleware($request);
            default:
                return null;
        }
    }

    /**
     * Auth middleware
     */
    private function authMiddleware(Request $request): ?Response
    {
        session_start();
        if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
            if ($request->isAjax()) {
                return Response::unauthorized();
            }
            return Response::redirect('/admin/login');
        }
        return null;
    }

    /**
     * Tenant middleware
     */
    private function tenantMiddleware(Request $request): ?Response
    {
        // Tenant context should already be initialized by Application
        return null;
    }

    /**
     * Call route handler
     */
    private function callHandler($handler, Request $request, array $params): Response
    {
        if (is_string($handler)) {
            // Controller@method format
            if (strpos($handler, '@') !== false) {
                [$controllerClass, $method] = explode('@', $handler);
                
                if (!class_exists($controllerClass)) {
                    return Response::error("Controller $controllerClass not found", 500);
                }

                $controller = new $controllerClass();
                
                if (!method_exists($controller, $method)) {
                    return Response::error("Method $method not found in $controllerClass", 500);
                }

                return $controller->$method($request, $params);
            }
        }

        if (is_callable($handler)) {
            return $handler($request, $params);
        }

        return Response::error('Invalid route handler', 500);
    }

    /**
     * Generate URL for named route
     */
    public function url(string $name, array $params = []): string
    {
        // Simple implementation - could be enhanced with named routes
        return '/' . ltrim($name, '/');
    }
}
