<?php

namespace App\Core\Http;

/**
 * HTTP Request Class
 * 
 * Handles HTTP request data and methods
 */
class Request
{
    private array $query;
    private array $request;
    private array $server;
    private array $files;
    private array $cookies;
    private array $headers;
    private ?string $content;

    public function __construct(
        array $query = [],
        array $request = [],
        array $server = [],
        array $files = [],
        array $cookies = []
    ) {
        $this->query = $query;
        $this->request = $request;
        $this->server = $server;
        $this->files = $files;
        $this->cookies = $cookies;
        $this->headers = $this->parseHeaders();
        $this->content = null;
    }

    /**
     * Create request from globals
     */
    public static function createFromGlobals(): self
    {
        return new self($_GET, $_POST, $_SERVER, $_FILES, $_COOKIE);
    }

    /**
     * Get request method
     */
    public function getMethod(): string
    {
        return strtoupper($this->server['REQUEST_METHOD'] ?? 'GET');
    }

    /**
     * Get request URI
     */
    public function getUri(): string
    {
        return $this->server['REQUEST_URI'] ?? '/';
    }

    /**
     * Get request path
     */
    public function getPath(): string
    {
        $uri = $this->getUri();
        $path = parse_url($uri, PHP_URL_PATH);

        // Remove the base directory from the path
        // For /gk_radevou/test -> /test
        // For /gk_radevou/api/services -> /api/services

        // Get the application base directory
        // SCRIPT_NAME: /gk_radevou/public/index.php
        // We want to remove: /gk_radevou
        $scriptName = $this->server['SCRIPT_NAME'] ?? '';
        $scriptDir = dirname($scriptName); // /gk_radevou/public
        $appBaseDir = dirname($scriptDir); // /gk_radevou

        // If we're in a subdirectory, remove it from the path
        if ($appBaseDir !== '/' && strpos($path, $appBaseDir) === 0) {
            $path = substr($path, strlen($appBaseDir));
        }

        // Ensure path starts with /
        $path = '/' . ltrim($path, '/');

        return $path ?: '/';
    }

    /**
     * Get query string
     */
    public function getQueryString(): string
    {
        return $this->server['QUERY_STRING'] ?? '';
    }

    /**
     * Get host
     */
    public function getHost(): string
    {
        return $this->server['HTTP_HOST'] ?? '';
    }

    /**
     * Get scheme
     */
    public function getScheme(): string
    {
        return $this->isSecure() ? 'https' : 'http';
    }

    /**
     * Check if request is secure
     */
    public function isSecure(): bool
    {
        return isset($this->server['HTTPS']) && $this->server['HTTPS'] !== 'off';
    }

    /**
     * Check if request is AJAX
     */
    public function isAjax(): bool
    {
        return $this->getHeader('X-Requested-With') === 'XMLHttpRequest';
    }

    /**
     * Check if request is JSON
     */
    public function isJson(): bool
    {
        $contentType = $this->getHeader('Content-Type');
        return $contentType && strpos($contentType, 'application/json') !== false;
    }

    /**
     * Get input value
     */
    public function input(string $key, $default = null)
    {
        // Check POST data first
        if (isset($this->request[$key])) {
            return $this->request[$key];
        }

        // Then check GET data
        if (isset($this->query[$key])) {
            return $this->query[$key];
        }

        // Check JSON input
        if ($this->isJson()) {
            $json = $this->json();
            if (isset($json[$key])) {
                return $json[$key];
            }
        }

        return $default;
    }

    /**
     * Get all input data
     */
    public function all(): array
    {
        $data = array_merge($this->query, $this->request);

        if ($this->isJson()) {
            $data = array_merge($data, $this->json());
        }

        return $data;
    }

    /**
     * Get only specified input keys
     */
    public function only(array $keys): array
    {
        $data = $this->all();
        return array_intersect_key($data, array_flip($keys));
    }

    /**
     * Get all except specified input keys
     */
    public function except(array $keys): array
    {
        $data = $this->all();
        return array_diff_key($data, array_flip($keys));
    }

    /**
     * Check if input key exists
     */
    public function has(string $key): bool
    {
        return $this->input($key) !== null;
    }

    /**
     * Get JSON input
     */
    public function json(): array
    {
        static $json = null;

        if ($json === null) {
            $content = $this->getContent();
            $json = $content ? json_decode($content, true) : [];
            if (json_last_error() !== JSON_ERROR_NONE) {
                $json = [];
            }
        }

        return $json;
    }

    /**
     * Get raw content
     */
    public function getContent(): string
    {
        if ($this->content === null) {
            $this->content = file_get_contents('php://input') ?: '';
        }
        return $this->content;
    }

    /**
     * Get header value
     */
    public function getHeader(string $name): ?string
    {
        $name = strtolower($name);
        return $this->headers[$name] ?? null;
    }

    /**
     * Get all headers
     */
    public function getHeaders(): array
    {
        return $this->headers;
    }

    /**
     * Get cookie value
     */
    public function cookie(string $name, $default = null)
    {
        return $this->cookies[$name] ?? $default;
    }

    /**
     * Get file upload
     */
    public function file(string $name): ?array
    {
        return $this->files[$name] ?? null;
    }

    /**
     * Get server value
     */
    public function server(string $name, $default = null)
    {
        return $this->server[$name] ?? $default;
    }

    /**
     * Get client IP address
     */
    public function getClientIp(): string
    {
        $ipKeys = [
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        ];

        foreach ($ipKeys as $key) {
            if (!empty($this->server[$key])) {
                $ips = explode(',', $this->server[$key]);
                return trim($ips[0]);
            }
        }

        return '0.0.0.0';
    }

    /**
     * Get user agent
     */
    public function getUserAgent(): string
    {
        return $this->server['HTTP_USER_AGENT'] ?? '';
    }

    /**
     * Parse headers from server variables
     */
    private function parseHeaders(): array
    {
        $headers = [];

        foreach ($this->server as $key => $value) {
            if (strpos($key, 'HTTP_') === 0) {
                $name = strtolower(str_replace('_', '-', substr($key, 5)));
                $headers[$name] = $value;
            }
        }

        return $headers;
    }

    /**
     * Validate input data
     */
    public function validate(array $rules): array
    {
        $data = $this->all();
        $errors = [];

        foreach ($rules as $field => $rule) {
            $value = $data[$field] ?? null;
            $ruleList = is_string($rule) ? explode('|', $rule) : $rule;

            foreach ($ruleList as $r) {
                if ($r === 'required' && empty($value)) {
                    $errors[$field][] = "The $field field is required.";
                } elseif ($r === 'email' && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    $errors[$field][] = "The $field must be a valid email address.";
                } elseif (strpos($r, 'min:') === 0) {
                    $min = (int)substr($r, 4);
                    if (strlen($value) < $min) {
                        $errors[$field][] = "The $field must be at least $min characters.";
                    }
                } elseif (strpos($r, 'max:') === 0) {
                    $max = (int)substr($r, 4);
                    if (strlen($value) > $max) {
                        $errors[$field][] = "The $field may not be greater than $max characters.";
                    }
                }
            }
        }

        if (!empty($errors)) {
            throw new \InvalidArgumentException('Validation failed: ' . json_encode($errors));
        }

        return $data;
    }
}
