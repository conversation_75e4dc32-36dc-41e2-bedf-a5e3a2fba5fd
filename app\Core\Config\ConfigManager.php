<?php

namespace App\Core\Config;

/**
 * Configuration Manager
 * 
 * Handles application configuration
 */
class ConfigManager
{
    private array $config = [];
    private bool $loaded = false;

    public function __construct()
    {
        $this->loadConfig();
    }

    /**
     * Load configuration
     */
    private function loadConfig(): void
    {
        if ($this->loaded) {
            return;
        }

        // Default configuration
        $this->config = [
            'app' => [
                'name' => 'Booking System',
                'version' => '2.0.0',
                'timezone' => 'Europe/Athens',
                'debug' => false
            ],
            'database' => [
                'path' => __DIR__ . '/../../../data/gk_booking.sqlite',
                'backup_path' => __DIR__ . '/../../../data/backups/'
            ],
            'session' => [
                'timeout' => 1800, // 30 minutes
                'name' => 'booking_session'
            ],
            'security' => [
                'verification_code_expiry' => 120, // 2 minutes
                'max_reservations_per_customer' => 5,
                'password_min_length' => 8
            ],
            'email' => [
                'from_email' => '<EMAIL>',
                'from_name' => 'Booking System'
            ],
            'business' => [
                'default_hours' => [
                    'Monday' => [['start' => '09:00', 'end' => '17:00']],
                    'Tuesday' => [['start' => '09:00', 'end' => '17:00']],
                    'Wednesday' => [['start' => '09:00', 'end' => '17:00']],
                    'Thursday' => [['start' => '09:00', 'end' => '17:00']],
                    'Friday' => [['start' => '09:00', 'end' => '17:00']],
                    'Saturday' => [],
                    'Sunday' => []
                ]
            ]
        ];

        // Load environment-specific config if exists
        $envConfig = $this->loadEnvironmentConfig();
        if ($envConfig) {
            $this->config = array_merge_recursive($this->config, $envConfig);
        }

        // Set timezone
        date_default_timezone_set($this->config['app']['timezone']);

        $this->loaded = true;
    }

    /**
     * Load environment-specific configuration
     */
    private function loadEnvironmentConfig(): ?array
    {
        $configFile = __DIR__ . '/../../../config/app.php';
        if (file_exists($configFile)) {
            return require $configFile;
        }
        return null;
    }

    /**
     * Get configuration value
     */
    public function get(string $key, $default = null)
    {
        $keys = explode('.', $key);
        $value = $this->config;

        foreach ($keys as $k) {
            if (!is_array($value) || !array_key_exists($k, $value)) {
                return $default;
            }
            $value = $value[$k];
        }

        return $value;
    }

    /**
     * Set configuration value
     */
    public function set(string $key, $value): void
    {
        $keys = explode('.', $key);
        $config = &$this->config;

        foreach ($keys as $k) {
            if (!isset($config[$k]) || !is_array($config[$k])) {
                $config[$k] = [];
            }
            $config = &$config[$k];
        }

        $config = $value;
    }

    /**
     * Check if configuration key exists
     */
    public function has(string $key): bool
    {
        $keys = explode('.', $key);
        $value = $this->config;

        foreach ($keys as $k) {
            if (!is_array($value) || !array_key_exists($k, $value)) {
                return false;
            }
            $value = $value[$k];
        }

        return true;
    }

    /**
     * Get all configuration
     */
    public function all(): array
    {
        return $this->config;
    }
}
