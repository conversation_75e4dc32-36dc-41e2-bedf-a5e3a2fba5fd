<?php

namespace App\Controllers\Api;

use App\Controllers\BaseController;
use App\Core\Http\Request;
use App\Core\Http\Response;
use App\Models\Customer;

/**
 * Customer API Controller
 * 
 * Handles customer-related API endpoints
 */
class CustomerController extends BaseController
{
    /**
     * Get all customers
     */
    public function index(Request $request): Response
    {
        try {
            $pagination = $this->getPaginationParams($request);
            $search = $request->input('search', '');
            
            $customers = Customer::search($search, $pagination['limit'], $pagination['offset']);
            $total = Customer::searchCount($search);
            
            return $this->paginatedResponse(
                $this->formatCollection($customers),
                $total,
                $pagination['page'],
                $pagination['limit']
            );
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get customer by ID
     */
    public function show(Request $request, array $params): Response
    {
        try {
            $customer = Customer::find($params['id']);
            
            if (!$customer) {
                return $this->notFound('Customer not found');
            }
            
            return $this->success($customer);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Create new customer
     */
    public function store(Request $request): Response
    {
        try {
            $data = $this->validate($request, [
                'name' => 'required',
                'email' => 'required|email',
                'mobile' => 'required'
            ]);

            // Check if email already exists
            if (Customer::findByEmail($data['email'])) {
                return $this->error('Customer with this email already exists', 409);
            }

            // Validate mobile format
            if (!Customer::validateMobile($data['mobile'])) {
                return $this->validationError(['mobile' => ['Invalid mobile number format']]);
            }

            $customer = Customer::create($data);
            
            return $this->created($customer, 'Customer created successfully');
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Update customer
     */
    public function update(Request $request, array $params): Response
    {
        try {
            $customer = Customer::find($params['id']);
            
            if (!$customer) {
                return $this->notFound('Customer not found');
            }

            $data = $this->validate($request, [
                'name' => 'required',
                'email' => 'required|email',
                'mobile' => 'required'
            ]);

            // Check if email already exists for another customer
            $existingCustomer = Customer::findByEmail($data['email']);
            if ($existingCustomer && $existingCustomer->id !== $customer->id) {
                return $this->error('Customer with this email already exists', 409);
            }

            // Validate mobile format
            if (!Customer::validateMobile($data['mobile'])) {
                return $this->validationError(['mobile' => ['Invalid mobile number format']]);
            }

            foreach ($data as $key => $value) {
                $customer->$key = $value;
            }

            $customer->save();
            
            return $this->updated($customer, 'Customer updated successfully');
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Delete customer
     */
    public function destroy(Request $request, array $params): Response
    {
        try {
            $customer = Customer::find($params['id']);
            
            if (!$customer) {
                return $this->notFound('Customer not found');
            }

            // Check if customer has reservations
            $reservations = $customer->reservations();
            if (!empty($reservations)) {
                return $this->error('Cannot delete customer with existing reservations', 409);
            }

            $customer->delete();
            
            return $this->deleted('Customer deleted successfully');
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get customer reservations
     */
    public function reservations(Request $request, array $params): Response
    {
        try {
            $customer = Customer::find($params['id']);
            
            if (!$customer) {
                return $this->notFound('Customer not found');
            }

            $reservations = $customer->reservations();
            
            return $this->success($this->formatCollection($reservations));
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get customer statistics
     */
    public function statistics(Request $request, array $params): Response
    {
        try {
            $customer = Customer::find($params['id']);
            
            if (!$customer) {
                return $this->notFound('Customer not found');
            }

            $stats = [
                'total_reservations' => $customer->getTotalReservations(),
                'total_spent' => $customer->getTotalSpent(),
                'last_visit' => $customer->last_visit
            ];
            
            return $this->success($stats);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Search customers by email or phone
     */
    public function search(Request $request): Response
    {
        try {
            $term = $request->input('q', '');
            $limit = min(10, max(1, (int)$request->input('limit', 10)));
            
            if (strlen($term) < 2) {
                return $this->success([]);
            }

            $customers = Customer::search($term, $limit);
            
            return $this->success($this->formatCollection($customers));
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Update customer statistics
     */
    public function updateStatistics(Request $request, array $params): Response
    {
        try {
            $customer = Customer::find($params['id']);
            
            if (!$customer) {
                return $this->notFound('Customer not found');
            }

            $customer->updateStatistics();
            
            return $this->success($customer, 'Customer statistics updated successfully');
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }
}
