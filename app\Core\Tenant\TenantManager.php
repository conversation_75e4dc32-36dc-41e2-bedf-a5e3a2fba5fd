<?php

namespace App\Core\Tenant;

use App\Core\Database\DatabaseManager;
use App\Models\Tenant;

/**
 * Tenant Manager
 * 
 * Handles tenant context and multi-tenancy
 */
class TenantManager
{
    private ?string $currentTenant = null;
    private ?array $tenantData = null;
    private DatabaseManager $database;

    public function __construct(DatabaseManager $database)
    {
        $this->database = $database;
    }

    /**
     * Initialize tenant from request
     */
    public function initializeFromRequest(): bool
    {
        // Method 1: Subdomain detection
        $tenantId = $this->getTenantFromSubdomain();

        // Method 2: Domain detection (fallback)
        if (!$tenantId) {
            $tenantId = $this->getTenantFromDomain();
        }

        // Method 3: Session/cookie (for admin panel)
        if (!$tenantId) {
            $tenantId = $this->getTenantFromSession();
        }

        // Method 4: Default tenant (development)
        if (!$tenantId) {
            $tenantId = $this->getDefaultTenant();
        }

        if ($tenantId) {
            $this->setTenant($tenantId);
            return true;
        }

        return false;
    }

    /**
     * Set current tenant
     */
    public function setTenant(string $tenantId): void
    {
        $this->currentTenant = $tenantId;
        $this->tenantData = null; // Reset cached data
    }

    /**
     * Get current tenant ID
     */
    public function getCurrentTenant(): ?string
    {
        return $this->currentTenant;
    }

    /**
     * Get tenant data
     */
    public function getTenantData(): ?array
    {
        if ($this->tenantData === null && $this->currentTenant !== null) {
            $this->loadTenantData();
        }
        return $this->tenantData;
    }

    /**
     * Require tenant context
     */
    public function requireTenant(): void
    {
        if ($this->currentTenant === null) {
            throw new \Exception('Tenant context is required but not set');
        }
    }

    /**
     * Get tenant from subdomain
     */
    private function getTenantFromSubdomain(): ?string
    {
        $host = $_SERVER['HTTP_HOST'] ?? '';
        
        // Check for subdomain pattern: tenant.domain.com
        if (preg_match('/^([a-zA-Z0-9-]+)\.skrtz\.gr$/', $host, $matches)) {
            $subdomain = $matches[1];
            
            // Skip www and common subdomains
            if (in_array($subdomain, ['www', 'mail', 'ftp', 'admin'])) {
                return null;
            }
            
            return $this->getTenantBySubdomain($subdomain);
        }
        
        return null;
    }

    /**
     * Get tenant from domain
     */
    private function getTenantFromDomain(): ?string
    {
        $host = $_SERVER['HTTP_HOST'] ?? '';
        return $this->getTenantByDomain($host);
    }

    /**
     * Get tenant from session
     */
    private function getTenantFromSession(): ?string
    {
        return $_SESSION['tenant_id'] ?? null;
    }

    /**
     * Get default tenant
     */
    private function getDefaultTenant(): ?string
    {
        // For development or fallback
        return 'realma';
    }

    /**
     * Get tenant by subdomain
     */
    private function getTenantBySubdomain(string $subdomain): ?string
    {
        try {
            $conn = $this->database->getConnection();
            $stmt = $conn->prepare("SELECT id FROM tenants WHERE subdomain = :subdomain AND status = 'active' LIMIT 1");
            $stmt->bindValue(':subdomain', $subdomain);
            $result = $stmt->execute();
            
            $row = $result->fetchArray(SQLITE3_ASSOC);
            return $row ? $row['id'] : null;
        } catch (\Exception $e) {
            error_log('Failed to get tenant by subdomain: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get tenant by domain
     */
    private function getTenantByDomain(string $domain): ?string
    {
        try {
            $conn = $this->database->getConnection();
            $stmt = $conn->prepare("SELECT id FROM tenants WHERE domain = :domain AND status = 'active' LIMIT 1");
            $stmt->bindValue(':domain', $domain);
            $result = $stmt->execute();
            
            $row = $result->fetchArray(SQLITE3_ASSOC);
            return $row ? $row['id'] : null;
        } catch (\Exception $e) {
            error_log('Failed to get tenant by domain: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Load tenant data from database
     */
    private function loadTenantData(): void
    {
        if (!$this->currentTenant) {
            return;
        }

        try {
            $conn = $this->database->getConnection();
            $stmt = $conn->prepare("SELECT * FROM tenants WHERE id = :tenant_id LIMIT 1");
            $stmt->bindValue(':tenant_id', $this->currentTenant);
            $result = $stmt->execute();

            $row = $result->fetchArray(SQLITE3_ASSOC);
            if ($row) {
                $this->tenantData = [
                    'id' => $row['id'],
                    'business_name' => $row['business_name'],
                    'domain' => $row['domain'],
                    'subdomain' => $row['subdomain'],
                    'plan' => $row['plan'],
                    'status' => $row['status'],
                    'settings' => json_decode($row['settings'] ?? '{}', true)
                ];
            }
        } catch (\Exception $e) {
            error_log('Failed to load tenant data: ' . $e->getMessage());
        }
    }

    /**
     * Get tenant setting
     */
    public function getSetting(string $key, $default = null)
    {
        $data = $this->getTenantData();
        return $data['settings'][$key] ?? $default;
    }

    /**
     * Set tenant setting
     */
    public function setSetting(string $key, $value): bool
    {
        $data = $this->getTenantData();
        if (!$data) {
            return false;
        }

        $settings = $data['settings'];
        $settings[$key] = $value;

        try {
            $conn = $this->database->getConnection();
            $stmt = $conn->prepare("UPDATE tenants SET settings = :settings, updated_at = :updated_at WHERE id = :tenant_id");
            $stmt->bindValue(':settings', json_encode($settings));
            $stmt->bindValue(':updated_at', date('Y-m-d H:i:s'));
            $stmt->bindValue(':tenant_id', $this->currentTenant);
            
            $result = $stmt->execute();
            
            if ($result) {
                $this->tenantData['settings'] = $settings;
            }
            
            return $result !== false;
        } catch (\Exception $e) {
            error_log('Failed to set tenant setting: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Create new tenant
     */
    public function createTenant(array $data): ?string
    {
        $tenantId = uniqid('tenant_', true);
        $now = date('Y-m-d H:i:s');

        try {
            $conn = $this->database->getConnection();
            $stmt = $conn->prepare("INSERT INTO tenants 
                (id, business_name, domain, subdomain, plan, status, billing_email, settings, created_at, updated_at) 
                VALUES (:id, :business_name, :domain, :subdomain, :plan, :status, :billing_email, :settings, :created_at, :updated_at)");
            
            $stmt->bindValue(':id', $tenantId);
            $stmt->bindValue(':business_name', $data['business_name']);
            $stmt->bindValue(':domain', $data['domain'] ?? null);
            $stmt->bindValue(':subdomain', $data['subdomain']);
            $stmt->bindValue(':plan', $data['plan'] ?? 'starter');
            $stmt->bindValue(':status', $data['status'] ?? 'trial');
            $stmt->bindValue(':billing_email', $data['billing_email'] ?? null);
            $stmt->bindValue(':settings', json_encode($data['settings'] ?? []));
            $stmt->bindValue(':created_at', $now);
            $stmt->bindValue(':updated_at', $now);
            
            $result = $stmt->execute();
            
            return $result ? $tenantId : null;
        } catch (\Exception $e) {
            error_log('Failed to create tenant: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Check if tenant exists
     */
    public function tenantExists(string $tenantId): bool
    {
        try {
            $conn = $this->database->getConnection();
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tenants WHERE id = :tenant_id");
            $stmt->bindValue(':tenant_id', $tenantId);
            $result = $stmt->execute();
            
            $row = $result->fetchArray(SQLITE3_ASSOC);
            return (int)($row['count'] ?? 0) > 0;
        } catch (\Exception $e) {
            error_log('Failed to check tenant existence: ' . $e->getMessage());
            return false;
        }
    }
}
