<?php

namespace App\Core\Tenant;

use App\Core\Application;

/**
 * Tenant Aware Trait
 * 
 * Provides tenant context functionality to models and classes
 */
trait TenantAware
{
    /**
     * Check if the class is tenant-aware
     */
    public function isTenantAware(): bool
    {
        return true;
    }

    /**
     * Get current tenant ID
     */
    public function getCurrentTenant(): ?string
    {
        return Application::getInstance()->getTenant()->getCurrentTenant();
    }

    /**
     * Require tenant context
     */
    public function requireTenant(): void
    {
        Application::getInstance()->getTenant()->requireTenant();
    }

    /**
     * Get tenant data
     */
    public function getTenantData(): ?array
    {
        return Application::getInstance()->getTenant()->getTenantData();
    }

    /**
     * Get tenant setting
     */
    public function getTenantSetting(string $key, $default = null)
    {
        return Application::getInstance()->getTenant()->getSetting($key, $default);
    }

    /**
     * Set tenant setting
     */
    public function setTenantSetting(string $key, $value): bool
    {
        return Application::getInstance()->getTenant()->setSetting($key, $value);
    }
}
