<?php

namespace App\Core\Model;

use App\Core\Application;
use App\Core\Database\QueryBuilder;
use App\Core\Tenant\TenantAware;

/**
 * Base Model Class
 * 
 * Provides common functionality for all models
 */
abstract class BaseModel
{
    use TenantAware;

    protected string $table = '';
    protected string $primaryKey = 'id';
    protected array $fillable = [];
    protected array $hidden = [];
    protected array $casts = [];
    protected bool $timestamps = true;
    protected array $attributes = [];
    protected array $original = [];
    protected bool $exists = false;

    public function __construct(array $attributes = [])
    {
        $this->fill($attributes);
        $this->syncOriginal();
    }

    /**
     * Get table name
     */
    public function getTable(): string
    {
        return $this->table;
    }

    /**
     * Get primary key
     */
    public function getKeyName(): string
    {
        return $this->primaryKey;
    }

    /**
     * Get primary key value
     */
    public function getKey()
    {
        return $this->getAttribute($this->getKeyName());
    }

    /**
     * Fill model with attributes
     */
    public function fill(array $attributes): self
    {
        foreach ($attributes as $key => $value) {
            if ($this->isFillable($key)) {
                $this->setAttribute($key, $value);
            }
        }
        return $this;
    }

    /**
     * Check if attribute is fillable
     */
    protected function isFillable(string $key): bool
    {
        return in_array($key, $this->fillable) || empty($this->fillable);
    }

    /**
     * Set attribute value
     */
    public function setAttribute(string $key, $value): void
    {
        $this->attributes[$key] = $value;
    }

    /**
     * Get attribute value
     */
    public function getAttribute(string $key)
    {
        $value = $this->attributes[$key] ?? null;
        
        if (isset($this->casts[$key])) {
            return $this->castAttribute($key, $value);
        }
        
        return $value;
    }

    /**
     * Cast attribute to specified type
     */
    protected function castAttribute(string $key, $value)
    {
        $castType = $this->casts[$key];
        
        switch ($castType) {
            case 'int':
            case 'integer':
                return (int) $value;
            case 'float':
            case 'double':
                return (float) $value;
            case 'bool':
            case 'boolean':
                return (bool) $value;
            case 'string':
                return (string) $value;
            case 'array':
            case 'json':
                return is_string($value) ? json_decode($value, true) : $value;
            default:
                return $value;
        }
    }

    /**
     * Get all attributes
     */
    public function getAttributes(): array
    {
        return $this->attributes;
    }

    /**
     * Convert model to array
     */
    public function toArray(): array
    {
        $attributes = $this->attributes;
        
        // Remove hidden attributes
        foreach ($this->hidden as $hidden) {
            unset($attributes[$hidden]);
        }
        
        // Apply casts
        foreach ($attributes as $key => $value) {
            if (isset($this->casts[$key])) {
                $attributes[$key] = $this->castAttribute($key, $value);
            }
        }
        
        return $attributes;
    }

    /**
     * Convert model to JSON
     */
    public function toJson(): string
    {
        return json_encode($this->toArray());
    }

    /**
     * Save model to database
     */
    public function save(): bool
    {
        if ($this->exists) {
            return $this->performUpdate();
        } else {
            return $this->performInsert();
        }
    }

    /**
     * Delete model from database
     */
    public function delete(): bool
    {
        if (!$this->exists) {
            return false;
        }
        
        $result = $this->newQuery()
            ->where($this->getKeyName(), '=', $this->getKey())
            ->delete();
            
        if ($result) {
            $this->exists = false;
        }
        
        return $result;
    }

    /**
     * Perform insert operation
     */
    protected function performInsert(): bool
    {
        $attributes = $this->attributes;
        
        // Add tenant_id if model is tenant-aware
        if ($this->isTenantAware()) {
            $attributes['tenant_id'] = $this->getCurrentTenant();
        }
        
        // Add timestamps
        if ($this->timestamps) {
            $now = date('Y-m-d H:i:s');
            $attributes['created_at'] = $now;
            $attributes['updated_at'] = $now;
        }
        
        // Generate ID if not set
        if (!isset($attributes[$this->primaryKey])) {
            $attributes[$this->primaryKey] = $this->generateId();
        }
        
        $result = $this->newQuery()->insert($attributes);
        
        if ($result) {
            $this->exists = true;
            $this->attributes = $attributes;
            $this->syncOriginal();
        }
        
        return $result;
    }

    /**
     * Perform update operation
     */
    protected function performUpdate(): bool
    {
        $dirty = $this->getDirty();
        
        if (empty($dirty)) {
            return true;
        }
        
        // Add updated timestamp
        if ($this->timestamps) {
            $dirty['updated_at'] = date('Y-m-d H:i:s');
        }
        
        $result = $this->newQuery()
            ->where($this->getKeyName(), '=', $this->getKey())
            ->update($dirty);
            
        if ($result) {
            $this->syncOriginal();
        }
        
        return $result;
    }

    /**
     * Get dirty attributes
     */
    protected function getDirty(): array
    {
        $dirty = [];
        
        foreach ($this->attributes as $key => $value) {
            if (!array_key_exists($key, $this->original) || $this->original[$key] !== $value) {
                $dirty[$key] = $value;
            }
        }
        
        return $dirty;
    }

    /**
     * Sync original attributes
     */
    protected function syncOriginal(): void
    {
        $this->original = $this->attributes;
    }

    /**
     * Generate new ID
     */
    protected function generateId(): string
    {
        return uniqid('', true);
    }

    /**
     * Create new query builder instance
     */
    public function newQuery(): QueryBuilder
    {
        $query = new QueryBuilder(Application::getInstance()->getDatabase()->getConnection());
        $query->table($this->getTable());
        
        // Add tenant scope if model is tenant-aware
        if ($this->isTenantAware()) {
            $query->where('tenant_id', '=', $this->getCurrentTenant());
        }
        
        return $query;
    }

    /**
     * Find model by ID
     */
    public static function find($id): ?static
    {
        $instance = new static();
        $result = $instance->newQuery()
            ->where($instance->getKeyName(), '=', $id)
            ->first();
            
        if ($result) {
            $model = new static($result);
            $model->exists = true;
            $model->syncOriginal();
            return $model;
        }
        
        return null;
    }

    /**
     * Get all models
     */
    public static function all(): array
    {
        $instance = new static();
        $results = $instance->newQuery()->get();
        
        $models = [];
        foreach ($results as $result) {
            $model = new static($result);
            $model->exists = true;
            $model->syncOriginal();
            $models[] = $model;
        }
        
        return $models;
    }

    /**
     * Create new model
     */
    public static function create(array $attributes): static
    {
        $model = new static($attributes);
        $model->save();
        return $model;
    }

    /**
     * Magic getter
     */
    public function __get(string $key)
    {
        return $this->getAttribute($key);
    }

    /**
     * Magic setter
     */
    public function __set(string $key, $value): void
    {
        $this->setAttribute($key, $value);
    }

    /**
     * Magic isset
     */
    public function __isset(string $key): bool
    {
        return isset($this->attributes[$key]);
    }

    /**
     * Magic unset
     */
    public function __unset(string $key): void
    {
        unset($this->attributes[$key]);
    }
}
