<?php

namespace App\Controllers;

use App\Core\Http\Request;
use App\Core\Http\Response;
use App\Core\Application;

/**
 * Base Controller
 * 
 * Provides common functionality for all controllers
 */
abstract class BaseController
{
    protected Application $app;

    public function __construct()
    {
        $this->app = Application::getInstance();
    }

    /**
     * Get current tenant
     */
    protected function getCurrentTenant(): ?string
    {
        return $this->app->getTenant()->getCurrentTenant();
    }

    /**
     * Get tenant data
     */
    protected function getTenantData(): ?array
    {
        return $this->app->getTenant()->getTenantData();
    }

    /**
     * Validate request data
     */
    protected function validate(Request $request, array $rules): array
    {
        try {
            return $request->validate($rules);
        } catch (\InvalidArgumentException $e) {
            $errors = json_decode($e->getMessage(), true);
            throw new \Exception('Validation failed', 422, $e);
        }
    }

    /**
     * Get pagination parameters
     */
    protected function getPaginationParams(Request $request): array
    {
        $page = max(1, (int)$request->input('page', 1));
        $limit = min(100, max(1, (int)$request->input('limit', 20)));
        $offset = ($page - 1) * $limit;

        return [
            'page' => $page,
            'limit' => $limit,
            'offset' => $offset
        ];
    }

    /**
     * Create paginated response
     */
    protected function paginatedResponse(array $data, int $total, int $page, int $limit): Response
    {
        $totalPages = ceil($total / $limit);

        return Response::json([
            'data' => $data,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => $totalPages,
                'total_items' => $total,
                'items_per_page' => $limit,
                'has_next' => $page < $totalPages,
                'has_prev' => $page > 1
            ]
        ]);
    }

    /**
     * Handle exceptions and return appropriate response
     */
    protected function handleException(\Exception $e): Response
    {
        if ($e->getCode() === 422) {
            return Response::validationError(json_decode($e->getMessage(), true));
        }

        if ($e->getCode() === 404) {
            return Response::notFound($e->getMessage());
        }

        if ($e->getCode() === 403) {
            return Response::forbidden($e->getMessage());
        }

        if ($e->getCode() === 401) {
            return Response::unauthorized($e->getMessage());
        }

        // Log error for debugging
        error_log("Controller Error: " . $e->getMessage() . " in " . $e->getFile() . ":" . $e->getLine());

        return Response::error('Internal server error', 500);
    }

    /**
     * Ensure session is started
     */
    protected function ensureSession(): void
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }

    /**
     * Check if user is authenticated (for admin controllers)
     */
    protected function requireAuth(): void
    {
        $this->ensureSession();
        if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
            throw new \Exception('Authentication required', 401);
        }
    }

    /**
     * Get current admin user
     */
    protected function getCurrentAdmin(): ?array
    {
        $this->ensureSession();
        return $_SESSION['admin_user'] ?? null;
    }

    /**
     * Render view (for non-API responses)
     */
    protected function render(string $view, array $data = [], string $layout = 'app'): Response
    {
        $viewPath = dirname(dirname(__DIR__)) . '/app/Views/' . str_replace('.', '/', $view) . '.php';

        if (!file_exists($viewPath)) {
            throw new \Exception("View not found: $view", 404);
        }

        // Extract data to variables
        extract($data);

        // Render the view content
        ob_start();
        include $viewPath;
        $content = ob_get_clean();

        // If no layout specified, return content directly
        if (!$layout) {
            return Response::html($content);
        }

        // Render with layout
        $layoutPath = dirname(dirname(__DIR__)) . '/app/Views/layouts/' . $layout . '.php';
        if (!file_exists($layoutPath)) {
            return Response::html($content); // Fallback to no layout
        }

        // Extract data again for layout
        extract($data);

        ob_start();
        include $layoutPath;
        $finalContent = ob_get_clean();

        return Response::html($finalContent);
    }

    /**
     * Get request filters
     */
    protected function getFilters(Request $request, array $allowed = []): array
    {
        $filters = [];

        foreach ($allowed as $filter) {
            $value = $request->input($filter);
            if ($value !== null && $value !== '') {
                $filters[$filter] = $value;
            }
        }

        return $filters;
    }

    /**
     * Format model for API response
     */
    protected function formatModel($model): array
    {
        if (is_array($model)) {
            return $model;
        }

        if (method_exists($model, 'toArray')) {
            return $model->toArray();
        }

        // Convert object to array
        $data = [];
        foreach (get_object_vars($model) as $key => $value) {
            if (!str_starts_with($key, '_')) {
                $data[$key] = $value;
            }
        }

        return $data;
    }

    /**
     * Format collection for API response
     */
    protected function formatCollection(array $collection): array
    {
        return array_map([$this, 'formatModel'], $collection);
    }

    /**
     * Success response
     */
    protected function success($data = null, string $message = 'Success'): Response
    {
        $response = ['success' => true, 'message' => $message];

        if ($data !== null) {
            $response['data'] = is_array($data) ? $data : $this->formatModel($data);
        }

        return Response::json($response);
    }

    /**
     * Error response
     */
    protected function error(string $message, int $code = 400): Response
    {
        return Response::json([
            'success' => false,
            'message' => $message
        ], $code);
    }

    /**
     * Created response
     */
    protected function created($data, string $message = 'Created successfully'): Response
    {
        return Response::json([
            'success' => true,
            'message' => $message,
            'data' => is_array($data) ? $data : $this->formatModel($data)
        ], 201);
    }

    /**
     * Updated response
     */
    protected function updated($data, string $message = 'Updated successfully'): Response
    {
        return Response::json([
            'success' => true,
            'message' => $message,
            'data' => is_array($data) ? $data : $this->formatModel($data)
        ]);
    }

    /**
     * Deleted response
     */
    protected function deleted(string $message = 'Deleted successfully'): Response
    {
        return Response::json([
            'success' => true,
            'message' => $message
        ]);
    }

    /**
     * Not found response
     */
    protected function notFound(string $message = 'Resource not found'): Response
    {
        return Response::json([
            'success' => false,
            'message' => $message
        ], 404);
    }

    /**
     * Forbidden response
     */
    protected function forbidden(string $message = 'Access forbidden'): Response
    {
        return Response::json([
            'success' => false,
            'message' => $message
        ], 403);
    }

    /**
     * Validation error response
     */
    protected function validationError(array $errors): Response
    {
        return Response::json([
            'success' => false,
            'message' => 'Validation failed',
            'errors' => $errors
        ], 422);
    }
}
