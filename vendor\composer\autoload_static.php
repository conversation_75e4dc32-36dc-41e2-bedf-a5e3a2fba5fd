<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticIniteb0410bad7edda355eb966c42679b223
{
    public static $prefixLengthsPsr4 = array (
        'A' => 
        array (
            'App\\' => 4,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'App\\' => 
        array (
            0 => __DIR__ . '/../..' . '/app',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticIniteb0410bad7edda355eb966c42679b223::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticIniteb0410bad7edda355eb966c42679b223::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticIniteb0410bad7edda355eb966c42679b223::$classMap;

        }, null, ClassLoader::class);
    }
}
