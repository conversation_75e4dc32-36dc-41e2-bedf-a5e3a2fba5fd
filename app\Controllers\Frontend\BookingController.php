<?php

namespace App\Controllers\Frontend;

use App\Controllers\BaseController;
use App\Core\Http\Request;
use App\Core\Http\Response;
use App\Models\Service;
use App\Models\Employee;
use App\Models\Customer;
use App\Models\Reservation;

/**
 * Frontend Booking Controller
 * 
 * Handles public booking functionality
 */
class BookingController extends BaseController
{
    /**
     * Show booking form
     */
    public function index(Request $request): Response
    {
        try {
            $services = Service::all();
            $tenantData = $this->getTenantData();

            if ($request->isAjax()) {
                return $this->success([
                    'services' => $this->formatCollection($services),
                    'business_info' => $tenantData
                ]);
            }

            return $this->render('frontend.booking', [
                'services' => $services,
                'business_info' => $tenantData,
                'debug_services_count' => count($services)
            ]);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get available services
     */
    public function services(Request $request): Response
    {
        try {
            $services = Service::all();

            $result = [];
            foreach ($services as $service) {
                $serviceData = $this->formatModel($service);
                $serviceData['formatted_duration'] = $service->getFormattedDuration();
                $serviceData['formatted_price'] = $service->getFormattedPrice();
                $serviceData['employees'] = $this->formatCollection($service->getAvailableEmployees());
                $result[] = $serviceData;
            }

            return $this->success($result);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get employees for service
     */
    public function employeesForService(Request $request, array $params): Response
    {
        try {
            $serviceName = urldecode($params['service']);
            $service = Service::findByName($serviceName);

            if (!$service) {
                return $this->notFound('Service not found');
            }

            $employees = $service->getAvailableEmployees();

            return $this->success($this->formatCollection($employees));
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get available time slots
     */
    public function availableSlots(Request $request): Response
    {
        try {
            $serviceName = $request->input('service');
            $date = $request->input('date');
            $employeeId = $request->input('employee_id');

            if (!$serviceName || !$date) {
                return $this->error('Service and date are required', 400);
            }

            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
                return $this->error('Invalid date format. Use YYYY-MM-DD', 400);
            }

            // Check if date is in the past
            if (strtotime($date) < strtotime(date('Y-m-d'))) {
                return $this->error('Cannot book appointments in the past', 400);
            }

            $service = Service::findByName($serviceName);
            if (!$service) {
                return $this->notFound('Service not found');
            }

            $availableSlots = $this->getAvailableTimeSlots($service, $date, $employeeId);

            return $this->success([
                'date' => $date,
                'service' => $serviceName,
                'employee_id' => $employeeId,
                'available_slots' => $availableSlots
            ]);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Create booking
     */
    public function book(Request $request): Response
    {
        try {
            $data = $this->validate($request, [
                'customer_name' => 'required',
                'customer_email' => 'required|email',
                'customer_mobile' => 'required',
                'service' => 'required',
                'date' => 'required',
                'time' => 'required'
            ]);

            // Validate service
            $service = Service::findByName($data['service']);
            if (!$service) {
                return $this->error('Service not found', 404);
            }

            // Validate date and time
            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $data['date'])) {
                return $this->error('Invalid date format', 400);
            }

            if (!preg_match('/^\d{2}:\d{2}$/', $data['time'])) {
                return $this->error('Invalid time format', 400);
            }

            // Check if date is in the past
            if (strtotime($data['date']) < strtotime(date('Y-m-d'))) {
                return $this->error('Cannot book appointments in the past', 400);
            }

            // Validate mobile format
            if (!Customer::validateMobile($data['customer_mobile'])) {
                return $this->validationError(['customer_mobile' => ['Invalid mobile number format']]);
            }

            // Find or create customer
            $customer = Customer::findByEmail($data['customer_email']);
            if (!$customer) {
                $customer = Customer::create([
                    'name' => $data['customer_name'],
                    'email' => $data['customer_email'],
                    'mobile' => $data['customer_mobile']
                ]);
            } else {
                // Update customer info if changed
                $customer->name = $data['customer_name'];
                $customer->mobile = $data['customer_mobile'];
                $customer->save();
            }

            // Validate employee if provided
            $employeeId = $request->input('employee_id');
            if ($employeeId) {
                $employee = Employee::find($employeeId);
                if (!$employee || !$employee->isActive()) {
                    return $this->error('Selected employee is not available', 400);
                }
            }

            // Check if time slot is still available
            $isAvailable = $this->isTimeSlotAvailable($service, $data['date'], $data['time'], $employeeId);
            if (!$isAvailable) {
                return $this->error('Selected time slot is no longer available', 409);
            }

            // Create reservation
            $reservation = Reservation::create([
                'customer_id' => $customer->id,
                'service' => $service->name,
                'date' => $data['date'],
                'time' => $data['time'],
                'duration' => $service->duration,
                'price' => $service->price,
                'employee_id' => $employeeId,
                'status' => Reservation::STATUS_PENDING
            ]);

            // Send confirmation email (if email service is configured)
            $this->sendBookingConfirmation($customer, $reservation);

            return $this->created([
                'reservation' => $this->formatModel($reservation),
                'customer' => $this->formatModel($customer)
            ], 'Booking created successfully');
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get booking details
     */
    public function details(Request $request, array $params): Response
    {
        try {
            $reservationId = $params['id'];
            $reservation = Reservation::find($reservationId);

            if (!$reservation) {
                return $this->notFound('Booking not found');
            }

            $customer = $reservation->customer();
            $employee = $reservation->employee();
            $service = $reservation->service();

            $data = $this->formatModel($reservation);
            $data['customer'] = $this->formatModel($customer);
            $data['employee'] = $employee ? $this->formatModel($employee) : null;
            $data['service_details'] = $service ? $this->formatModel($service) : null;
            $data['formatted_date'] = $reservation->getFormattedDate();
            $data['time_range'] = $reservation->getTimeRange();

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Cancel booking
     */
    public function cancel(Request $request, array $params): Response
    {
        try {
            $reservationId = $params['id'];
            $reservation = Reservation::find($reservationId);

            if (!$reservation) {
                return $this->notFound('Booking not found');
            }

            if ($reservation->isCompleted()) {
                return $this->error('Completed bookings cannot be cancelled', 400);
            }

            if ($reservation->isCancelled()) {
                return $this->error('Booking is already cancelled', 400);
            }

            // Check if cancellation is allowed (e.g., not too close to appointment time)
            $appointmentTime = strtotime($reservation->date . ' ' . $reservation->time);
            $now = time();
            $hoursUntilAppointment = ($appointmentTime - $now) / 3600;

            if ($hoursUntilAppointment < 2) {
                return $this->error('Bookings cannot be cancelled less than 2 hours before the appointment', 400);
            }

            $reservation->cancel();

            // Send cancellation email
            $customer = $reservation->customer();
            $this->sendCancellationNotification($customer, $reservation);

            return $this->success(null, 'Booking cancelled successfully');
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get available time slots for service and date
     */
    private function getAvailableTimeSlots(Service $service, string $date, ?string $employeeId = null): array
    {
        $dayOfWeek = date('l', strtotime($date));
        $tenantData = $this->getTenantData();
        $businessHours = $tenantData['settings']['business_hours'] ?? [];

        $dayHours = $businessHours[$dayOfWeek] ?? [];
        if (empty($dayHours)) {
            return [];
        }

        // Get existing reservations for this date
        $existingReservations = Reservation::forDate($date);

        // If specific employee requested, filter by employee availability
        if ($employeeId) {
            $employee = Employee::find($employeeId);
            if ($employee) {
                $employeeHours = $employee->getWorkingHours($dayOfWeek);
                if (!empty($employeeHours)) {
                    $dayHours = $employeeHours;
                }
            }
        }

        $availableSlots = [];
        foreach ($dayHours as $period) {
            $start = strtotime($period['start']);
            $end = strtotime($period['end']);

            // Generate 15-minute time slots
            $current = $start;
            while ($current + ($service->duration * 60) <= $end) {
                $timeSlot = date('H:i', $current);

                // Check if this slot conflicts with existing reservations
                $isAvailable = true;
                foreach ($existingReservations as $existing) {
                    if ($existing->status === Reservation::STATUS_CANCELLED) {
                        continue;
                    }

                    // If employee specified, only check conflicts for same employee
                    if ($employeeId && $existing->employee_id !== $employeeId) {
                        continue;
                    }

                    $existingStart = strtotime($existing->time);
                    $existingEnd = $existingStart + ($existing->duration * 60);
                    $slotEnd = $current + ($service->duration * 60);

                    if (($current < $existingEnd) && ($slotEnd > $existingStart)) {
                        $isAvailable = false;
                        break;
                    }
                }

                if ($isAvailable) {
                    $availableSlots[] = $timeSlot;
                }

                $current += 15 * 60; // 15 minutes
            }
        }

        return $availableSlots;
    }

    /**
     * Check if time slot is available
     */
    private function isTimeSlotAvailable(Service $service, string $date, string $time, ?string $employeeId = null): bool
    {
        $availableSlots = $this->getAvailableTimeSlots($service, $date, $employeeId);
        return in_array($time, $availableSlots);
    }

    /**
     * Send booking confirmation email
     */
    private function sendBookingConfirmation(Customer $customer, Reservation $reservation): void
    {
        // TODO: Implement email sending
        // This would integrate with the email service configured in tenant settings
    }

    /**
     * Send cancellation notification
     */
    private function sendCancellationNotification(Customer $customer, Reservation $reservation): void
    {
        // TODO: Implement email sending
        // This would integrate with the email service configured in tenant settings
    }
}
