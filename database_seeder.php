<?php

/**
 * Database Seeder - Add sample data for testing
 */

require_once 'vendor/autoload.php';

use App\Core\Application;
use App\Models\Service;
use App\Models\Employee;

try {
    // Initialize application
    $app = Application::getInstance();
    $app->initialize();

    echo "🌱 Seeding database with sample data...\n\n";

    // Sample Services
    $services = [
        [
            'name' => 'Κούρεμα Ανδρικό',
            'description' => 'Κλασικό ανδρικό κούρεμα με ψαλίδι και μηχανή',
            'duration' => 30,
            'price' => 15.00,
            'is_active' => 1,
            'tenant_id' => 'realma'
        ],
        [
            'name' => 'Κούρεμα Γυναικείο',
            'description' => 'Γυναικείο κούρεμα και styling',
            'duration' => 45,
            'price' => 25.00,
            'is_active' => 1,
            'tenant_id' => 'realma'
        ],
        [
            'name' => 'Βαφή Μαλλιών',
            'description' => 'Βαφή μαλλιών με επαγγελματικά προϊόντα',
            'duration' => 90,
            'price' => 45.00,
            'is_active' => 1,
            'tenant_id' => 'realma'
        ],
        [
            'name' => 'Φρυδάκια',
            'description' => 'Διαμόρφωση φρυδιών με κλωστή',
            'duration' => 20,
            'price' => 8.00,
            'is_active' => 1,
            'tenant_id' => 'realma'
        ],
        [
            'name' => 'Μανικιούρ',
            'description' => 'Κλασικό μανικιούρ με βερνίκι',
            'duration' => 40,
            'price' => 20.00,
            'is_active' => 1,
            'tenant_id' => 'realma'
        ],
        [
            'name' => 'Πεντικιούρ',
            'description' => 'Πεντικιούρ με περιποίηση ποδιών',
            'duration' => 50,
            'price' => 25.00,
            'is_active' => 1,
            'tenant_id' => 'realma'
        ]
    ];

    // Sample Employees
    $now = date('Y-m-d H:i:s');
    $employees = [
        [
            'name' => 'Μαρία Παπαδοπούλου',
            'email' => '<EMAIL>',
            'phone' => '6912345678',
            'created_at' => $now,
            'tenant_id' => 'realma'
        ],
        [
            'name' => 'Γιάννης Κωνσταντίνου',
            'email' => '<EMAIL>',
            'phone' => '6987654321',
            'created_at' => $now,
            'tenant_id' => 'realma'
        ],
        [
            'name' => 'Ελένη Γεωργίου',
            'email' => '<EMAIL>',
            'phone' => '6911223344',
            'created_at' => $now,
            'tenant_id' => 'realma'
        ]
    ];

    // Insert Services
    echo "📋 Adding services...\n";
    foreach ($services as $serviceData) {
        try {
            $existing = Service::findByName($serviceData['name']);
            if (!$existing) {
                $service = Service::create($serviceData);
                echo "  ✅ Added service: {$serviceData['name']}\n";
            } else {
                echo "  ⏭️  Service already exists: {$serviceData['name']}\n";
            }
        } catch (Exception $e) {
            echo "  ❌ Error adding service {$serviceData['name']}: " . $e->getMessage() . "\n";
        }
    }

    echo "\n👥 Adding employees...\n";
    foreach ($employees as $employeeData) {
        try {
            // Check if employee exists by email (we'll need to add this method)
            $service = new Employee();
            $existing = $service->newQuery()->where('email', '=', $employeeData['email'])->first();
            if (!$existing) {
                $employee = Employee::create($employeeData);
                echo "  ✅ Added employee: {$employeeData['name']}\n";
            } else {
                echo "  ⏭️  Employee already exists: {$employeeData['name']}\n";
            }
        } catch (Exception $e) {
            echo "  ❌ Error adding employee {$employeeData['name']}: " . $e->getMessage() . "\n";
        }
    }

    echo "\n🎉 Database seeding completed successfully!\n";
    echo "\nYou can now:\n";
    echo "- Visit http://localhost/gk_radevou/ to see the booking form\n";
    echo "- Visit http://localhost/gk_radevou/api/services to see the services API\n";
    echo "- Visit http://localhost/gk_radevou/admin to access the admin panel\n";
    echo "  (Default admin: username=admin, password=admin123)\n\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
