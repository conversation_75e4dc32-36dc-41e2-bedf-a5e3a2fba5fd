<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Core\Http\Request;
use App\Core\Http\Response;
use App\Models\Customer;
use App\Models\Reservation;
use App\Models\Employee;
use App\Models\Service;

/**
 * Admin Dashboard Controller
 * 
 * Handles admin dashboard functionality
 */
class DashboardController extends BaseController
{
    /**
     * Show dashboard
     */
    public function index(Request $request): Response
    {
        try {
            $this->requireAuth();

            $stats = $this->getDashboardStats();
            $recentReservations = $this->getRecentReservations();
            $upcomingReservations = $this->getUpcomingReservations();
            $popularServices = $this->getPopularServices();

            if ($request->isAjax()) {
                return $this->success([
                    'stats' => $stats,
                    'recent_reservations' => $recentReservations,
                    'upcoming_reservations' => $upcomingReservations,
                    'popular_services' => $popularServices
                ]);
            }

            $router = \App\Core\Application::getInstance()->getRouter();
            return $this->render('admin.dashboard', [
                'stats' => $stats,
                'recent_reservations' => $recentReservations,
                'upcoming_reservations' => $upcomingReservations,
                'popular_services' => $popularServices,
                'base_url' => $router->url(''),
                'business_info' => $this->getTenantData(),
                'admin_user' => $this->getCurrentAdmin()
            ], '');
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get dashboard statistics
     */
    public function stats(Request $request): Response
    {
        try {
            $this->requireAuth();

            $stats = $this->getDashboardStats();

            return $this->success($stats);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get revenue chart data
     */
    public function revenueChart(Request $request): Response
    {
        try {
            $this->requireAuth();

            $period = $request->input('period', 'month'); // month, week, year
            $chartData = $this->getRevenueChartData($period);

            return $this->success($chartData);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get bookings chart data
     */
    public function bookingsChart(Request $request): Response
    {
        try {
            $this->requireAuth();

            $period = $request->input('period', 'month');
            $chartData = $this->getBookingsChartData($period);

            return $this->success($chartData);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get dashboard statistics
     */
    private function getDashboardStats(): array
    {
        // Today's stats
        $today = date('Y-m-d');
        $todayReservations = Reservation::forDate($today);
        $todayRevenue = array_sum(array_map(function ($r) {
            return $r->status === Reservation::STATUS_COMPLETED ? $r->price : 0;
        }, $todayReservations));

        // This month's stats
        $monthStart = date('Y-m-01');
        $monthEnd = date('Y-m-t');
        $monthStats = Reservation::getRevenueStats($monthStart, $monthEnd);

        // Last month's stats for comparison
        $lastMonthStart = date('Y-m-01', strtotime('-1 month'));
        $lastMonthEnd = date('Y-m-t', strtotime('-1 month'));
        $lastMonthStats = Reservation::getRevenueStats($lastMonthStart, $lastMonthEnd);

        // Calculate growth percentages
        $revenueGrowth = $lastMonthStats['total_revenue'] > 0
            ? (($monthStats['total_revenue'] - $lastMonthStats['total_revenue']) / $lastMonthStats['total_revenue']) * 100
            : 0;

        $bookingsGrowth = $lastMonthStats['total_reservations'] > 0
            ? (($monthStats['total_reservations'] - $lastMonthStats['total_reservations']) / $lastMonthStats['total_reservations']) * 100
            : 0;

        // Total counts
        $totalCustomers = Customer::searchCount();
        $totalEmployees = Employee::searchCount();
        $totalServices = Service::searchCount();

        return [
            'today' => [
                'reservations' => count($todayReservations),
                'revenue' => $todayRevenue
            ],
            'month' => [
                'reservations' => $monthStats['total_reservations'],
                'revenue' => $monthStats['total_revenue'],
                'average_value' => $monthStats['average_value']
            ],
            'growth' => [
                'revenue' => round($revenueGrowth, 1),
                'bookings' => round($bookingsGrowth, 1)
            ],
            'totals' => [
                'customers' => $totalCustomers,
                'employees' => $totalEmployees,
                'services' => $totalServices
            ]
        ];
    }

    /**
     * Get recent reservations
     */
    private function getRecentReservations(): array
    {
        $reservations = Reservation::search([], 10, 0);

        foreach ($reservations as &$reservation) {
            $customer = $reservation->customer();
            $employee = $reservation->employee();

            $reservation = $this->formatModel($reservation);
            $reservation['customer'] = $customer ? $this->formatModel($customer) : null;
            $reservation['employee'] = $employee ? $this->formatModel($employee) : null;
        }

        return $reservations;
    }

    /**
     * Get upcoming reservations
     */
    private function getUpcomingReservations(): array
    {
        $reservations = Reservation::upcoming(10);

        foreach ($reservations as &$reservation) {
            $customer = $reservation->customer();
            $employee = $reservation->employee();

            $reservation = $this->formatModel($reservation);
            $reservation['customer'] = $customer ? $this->formatModel($customer) : null;
            $reservation['employee'] = $employee ? $this->formatModel($employee) : null;
        }

        return $reservations;
    }

    /**
     * Get popular services
     */
    private function getPopularServices(): array
    {
        $services = Service::getPopular(5);

        $result = [];
        foreach ($services as $service) {
            $stats = $service->getStatistics();
            $result[] = [
                'service' => $this->formatModel($service),
                'bookings' => $stats['total_bookings'],
                'revenue' => $stats['total_revenue']
            ];
        }

        return $result;
    }

    /**
     * Get revenue chart data
     */
    private function getRevenueChartData(string $period): array
    {
        $data = [];
        $labels = [];

        switch ($period) {
            case 'week':
                // Last 7 days
                for ($i = 6; $i >= 0; $i--) {
                    $date = date('Y-m-d', strtotime("-$i days"));
                    $reservations = Reservation::forDate($date);
                    $revenue = array_sum(array_map(function ($r) {
                        return $r->status === Reservation::STATUS_COMPLETED ? $r->price : 0;
                    }, $reservations));

                    $labels[] = date('M j', strtotime($date));
                    $data[] = $revenue;
                }
                break;

            case 'year':
                // Last 12 months
                for ($i = 11; $i >= 0; $i--) {
                    $monthStart = date('Y-m-01', strtotime("-$i months"));
                    $monthEnd = date('Y-m-t', strtotime("-$i months"));
                    $stats = Reservation::getRevenueStats($monthStart, $monthEnd);

                    $labels[] = date('M Y', strtotime($monthStart));
                    $data[] = $stats['total_revenue'];
                }
                break;

            default: // month
                // Last 30 days
                for ($i = 29; $i >= 0; $i--) {
                    $date = date('Y-m-d', strtotime("-$i days"));
                    $reservations = Reservation::forDate($date);
                    $revenue = array_sum(array_map(function ($r) {
                        return $r->status === Reservation::STATUS_COMPLETED ? $r->price : 0;
                    }, $reservations));

                    $labels[] = date('M j', strtotime($date));
                    $data[] = $revenue;
                }
                break;
        }

        return [
            'labels' => $labels,
            'data' => $data
        ];
    }

    /**
     * Get bookings chart data
     */
    private function getBookingsChartData(string $period): array
    {
        $data = [];
        $labels = [];

        switch ($period) {
            case 'week':
                // Last 7 days
                for ($i = 6; $i >= 0; $i--) {
                    $date = date('Y-m-d', strtotime("-$i days"));
                    $reservations = Reservation::forDate($date);

                    $labels[] = date('M j', strtotime($date));
                    $data[] = count($reservations);
                }
                break;

            case 'year':
                // Last 12 months
                for ($i = 11; $i >= 0; $i--) {
                    $monthStart = date('Y-m-01', strtotime("-$i months"));
                    $monthEnd = date('Y-m-t', strtotime("-$i months"));
                    $stats = Reservation::getRevenueStats($monthStart, $monthEnd);

                    $labels[] = date('M Y', strtotime($monthStart));
                    $data[] = $stats['total_reservations'];
                }
                break;

            default: // month
                // Last 30 days
                for ($i = 29; $i >= 0; $i--) {
                    $date = date('Y-m-d', strtotime("-$i days"));
                    $reservations = Reservation::forDate($date);

                    $labels[] = date('M j', strtotime($date));
                    $data[] = count($reservations);
                }
                break;
        }

        return [
            'labels' => $labels,
            'data' => $data
        ];
    }
}
