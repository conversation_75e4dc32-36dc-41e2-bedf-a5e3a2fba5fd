<?php

namespace App\Models;

use App\Core\Model\BaseModel;

/**
 * Reservation Model
 * 
 * Represents a booking reservation
 */
class Reservation extends BaseModel
{
    protected string $table = 'reservations';
    
    protected array $fillable = [
        'customer_id',
        'service',
        'date',
        'time',
        'duration',
        'price',
        'status',
        'employee_id'
    ];

    protected array $casts = [
        'duration' => 'int',
        'price' => 'float',
        'created_at' => 'string'
    ];

    const STATUS_PENDING = 'pending';
    const STATUS_CONFIRMED = 'confirmed';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_NO_SHOW = 'no_show';

    /**
     * Generate reservation ID
     */
    protected function generateId(): string
    {
        return 'RES' . strtoupper(uniqid());
    }

    /**
     * Get customer
     */
    public function customer(): ?Customer
    {
        return Customer::find($this->customer_id);
    }

    /**
     * Get employee
     */
    public function employee(): ?Employee
    {
        return $this->employee_id ? Employee::find($this->employee_id) : null;
    }

    /**
     * Get service details
     */
    public function service(): ?Service
    {
        return Service::findByName($this->service);
    }

    /**
     * Check if reservation is pending
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if reservation is confirmed
     */
    public function isConfirmed(): bool
    {
        return $this->status === self::STATUS_CONFIRMED;
    }

    /**
     * Check if reservation is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * Check if reservation is cancelled
     */
    public function isCancelled(): bool
    {
        return $this->status === self::STATUS_CANCELLED;
    }

    /**
     * Confirm reservation
     */
    public function confirm(): bool
    {
        $this->status = self::STATUS_CONFIRMED;
        return $this->save();
    }

    /**
     * Complete reservation
     */
    public function complete(): bool
    {
        $this->status = self::STATUS_COMPLETED;
        $result = $this->save();
        
        if ($result) {
            // Update customer statistics
            $customer = $this->customer();
            if ($customer) {
                $customer->updateStatistics();
            }
        }
        
        return $result;
    }

    /**
     * Cancel reservation
     */
    public function cancel(): bool
    {
        $this->status = self::STATUS_CANCELLED;
        return $this->save();
    }

    /**
     * Mark as no show
     */
    public function markNoShow(): bool
    {
        $this->status = self::STATUS_NO_SHOW;
        return $this->save();
    }

    /**
     * Get reservation end time
     */
    public function getEndTime(): string
    {
        $startTime = strtotime($this->time);
        $endTime = $startTime + ($this->duration * 60);
        return date('H:i', $endTime);
    }

    /**
     * Get formatted date
     */
    public function getFormattedDate(): string
    {
        return date('d/m/Y', strtotime($this->date));
    }

    /**
     * Get formatted time range
     */
    public function getTimeRange(): string
    {
        return $this->time . ' - ' . $this->getEndTime();
    }

    /**
     * Check if reservation conflicts with another
     */
    public function conflictsWith(Reservation $other): bool
    {
        if ($this->date !== $other->date) {
            return false;
        }

        if ($this->employee_id && $other->employee_id && $this->employee_id !== $other->employee_id) {
            return false;
        }

        $thisStart = strtotime($this->time);
        $thisEnd = $thisStart + ($this->duration * 60);
        $otherStart = strtotime($other->time);
        $otherEnd = $otherStart + ($other->duration * 60);

        return ($thisStart < $otherEnd) && ($thisEnd > $otherStart);
    }

    /**
     * Get reservations for date
     */
    public static function forDate(string $date): array
    {
        $instance = new static();
        $results = $instance->newQuery()
            ->where('date', '=', $date)
            ->where('status', '!=', self::STATUS_CANCELLED)
            ->orderBy('time')
            ->get();

        $reservations = [];
        foreach ($results as $result) {
            $model = new static($result);
            $model->exists = true;
            $model->syncOriginal();
            $reservations[] = $model;
        }

        return $reservations;
    }

    /**
     * Get reservations for employee and date
     */
    public static function forEmployeeAndDate(string $employeeId, string $date): array
    {
        $instance = new static();
        $results = $instance->newQuery()
            ->where('employee_id', '=', $employeeId)
            ->where('date', '=', $date)
            ->where('status', '!=', self::STATUS_CANCELLED)
            ->orderBy('time')
            ->get();

        $reservations = [];
        foreach ($results as $result) {
            $model = new static($result);
            $model->exists = true;
            $model->syncOriginal();
            $reservations[] = $model;
        }

        return $reservations;
    }

    /**
     * Get upcoming reservations
     */
    public static function upcoming(int $limit = 10): array
    {
        $today = date('Y-m-d');
        $instance = new static();
        $results = $instance->newQuery()
            ->where('date', '>=', $today)
            ->where('status', 'IN', [self::STATUS_PENDING, self::STATUS_CONFIRMED])
            ->orderBy('date')
            ->orderBy('time')
            ->limit($limit)
            ->get();

        $reservations = [];
        foreach ($results as $result) {
            $model = new static($result);
            $model->exists = true;
            $model->syncOriginal();
            $reservations[] = $model;
        }

        return $reservations;
    }

    /**
     * Search reservations
     */
    public static function search(array $filters = [], int $limit = 20, int $offset = 0): array
    {
        $instance = new static();
        $query = $instance->newQuery();

        if (!empty($filters['customer_name'])) {
            // Join with customers table for name search
            $query->join('customers', 'reservations.customer_id', '=', 'customers.id')
                  ->where('customers.name', 'LIKE', '%' . $filters['customer_name'] . '%');
        }

        if (!empty($filters['service'])) {
            $query->where('service', 'LIKE', '%' . $filters['service'] . '%');
        }

        if (!empty($filters['status'])) {
            $query->where('status', '=', $filters['status']);
        }

        if (!empty($filters['date_from'])) {
            $query->where('date', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->where('date', '<=', $filters['date_to']);
        }

        if (!empty($filters['employee_id'])) {
            $query->where('employee_id', '=', $filters['employee_id']);
        }

        $results = $query->orderBy('date', 'DESC')
                        ->orderBy('time', 'DESC')
                        ->limit($limit)
                        ->offset($offset)
                        ->get();

        $reservations = [];
        foreach ($results as $result) {
            $model = new static($result);
            $model->exists = true;
            $model->syncOriginal();
            $reservations[] = $model;
        }

        return $reservations;
    }

    /**
     * Get search count
     */
    public static function searchCount(array $filters = []): int
    {
        $instance = new static();
        $query = $instance->newQuery();

        if (!empty($filters['customer_name'])) {
            $query->join('customers', 'reservations.customer_id', '=', 'customers.id')
                  ->where('customers.name', 'LIKE', '%' . $filters['customer_name'] . '%');
        }

        if (!empty($filters['service'])) {
            $query->where('service', 'LIKE', '%' . $filters['service'] . '%');
        }

        if (!empty($filters['status'])) {
            $query->where('status', '=', $filters['status']);
        }

        if (!empty($filters['date_from'])) {
            $query->where('date', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->where('date', '<=', $filters['date_to']);
        }

        if (!empty($filters['employee_id'])) {
            $query->where('employee_id', '=', $filters['employee_id']);
        }

        return $query->count();
    }

    /**
     * Get revenue statistics
     */
    public static function getRevenueStats(string $dateFrom, string $dateTo): array
    {
        $instance = new static();
        $results = $instance->newQuery()
            ->where('date', '>=', $dateFrom)
            ->where('date', '<=', $dateTo)
            ->where('status', '=', self::STATUS_COMPLETED)
            ->get();

        $total = 0;
        $count = 0;
        $byService = [];

        foreach ($results as $result) {
            $total += $result['price'];
            $count++;
            
            $service = $result['service'];
            if (!isset($byService[$service])) {
                $byService[$service] = ['count' => 0, 'revenue' => 0];
            }
            $byService[$service]['count']++;
            $byService[$service]['revenue'] += $result['price'];
        }

        return [
            'total_revenue' => $total,
            'total_reservations' => $count,
            'average_value' => $count > 0 ? $total / $count : 0,
            'by_service' => $byService
        ];
    }
}
