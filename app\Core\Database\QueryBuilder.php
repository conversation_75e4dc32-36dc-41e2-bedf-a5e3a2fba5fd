<?php

namespace App\Core\Database;

use SQLite3;
use SQLite3Result;

/**
 * Query Builder
 * 
 * Provides fluent interface for building SQL queries
 */
class QueryBuilder
{
    private SQLite3 $connection;
    private string $table = '';
    private array $select = ['*'];
    private array $where = [];
    private array $joins = [];
    private array $orderBy = [];
    private array $groupBy = [];
    private ?int $limit = null;
    private ?int $offset = null;
    private array $bindings = [];

    public function __construct(SQLite3 $connection)
    {
        $this->connection = $connection;
    }

    /**
     * Set table name
     */
    public function table(string $table): self
    {
        $this->table = $table;
        return $this;
    }

    /**
     * Set select columns
     */
    public function select(array $columns = ['*']): self
    {
        $this->select = $columns;
        return $this;
    }

    /**
     * Add where condition
     */
    public function where(string $column, string $operator, $value): self
    {
        $this->where[] = [
            'type' => 'AND',
            'column' => $column,
            'operator' => $operator,
            'value' => $value
        ];
        return $this;
    }

    /**
     * Add OR where condition
     */
    public function orWhere(string $column, string $operator, $value): self
    {
        $this->where[] = [
            'type' => 'OR',
            'column' => $column,
            'operator' => $operator,
            'value' => $value
        ];
        return $this;
    }

    /**
     * Add where in condition
     */
    public function whereIn(string $column, array $values): self
    {
        $this->where[] = [
            'type' => 'AND',
            'column' => $column,
            'operator' => 'IN',
            'value' => $values
        ];
        return $this;
    }

    /**
     * Add join
     */
    public function join(string $table, string $first, string $operator, string $second): self
    {
        $this->joins[] = [
            'type' => 'INNER',
            'table' => $table,
            'first' => $first,
            'operator' => $operator,
            'second' => $second
        ];
        return $this;
    }

    /**
     * Add left join
     */
    public function leftJoin(string $table, string $first, string $operator, string $second): self
    {
        $this->joins[] = [
            'type' => 'LEFT',
            'table' => $table,
            'first' => $first,
            'operator' => $operator,
            'second' => $second
        ];
        return $this;
    }

    /**
     * Add order by
     */
    public function orderBy(string $column, string $direction = 'ASC'): self
    {
        $this->orderBy[] = ['column' => $column, 'direction' => $direction];
        return $this;
    }

    /**
     * Add group by
     */
    public function groupBy(string $column): self
    {
        $this->groupBy[] = $column;
        return $this;
    }

    /**
     * Set limit
     */
    public function limit(int $limit): self
    {
        $this->limit = $limit;
        return $this;
    }

    /**
     * Set offset
     */
    public function offset(int $offset): self
    {
        $this->offset = $offset;
        return $this;
    }

    /**
     * Execute select query and get all results
     */
    public function get(): array
    {
        $sql = $this->buildSelectQuery();
        $stmt = $this->connection->prepare($sql);
        $this->bindParameters($stmt);
        $result = $stmt->execute();

        $rows = [];
        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $rows[] = $row;
        }

        return $rows;
    }

    /**
     * Execute select query and get first result
     */
    public function first(): ?array
    {
        $this->limit(1);
        $results = $this->get();
        return $results[0] ?? null;
    }

    /**
     * Get count of records
     */
    public function count(): int
    {
        $originalSelect = $this->select;
        $this->select = ['COUNT(*) as count'];
        
        $sql = $this->buildSelectQuery();
        $stmt = $this->connection->prepare($sql);
        $this->bindParameters($stmt);
        $result = $stmt->execute();
        
        $row = $result->fetchArray(SQLITE3_ASSOC);
        $this->select = $originalSelect;
        
        return (int)($row['count'] ?? 0);
    }

    /**
     * Insert record
     */
    public function insert(array $data): bool
    {
        $columns = array_keys($data);
        $placeholders = array_fill(0, count($columns), '?');
        
        $sql = "INSERT INTO {$this->table} (" . implode(', ', $columns) . ") VALUES (" . implode(', ', $placeholders) . ")";
        
        $stmt = $this->connection->prepare($sql);
        $i = 1;
        foreach ($data as $value) {
            $stmt->bindValue($i++, $value);
        }
        
        return $stmt->execute() !== false;
    }

    /**
     * Update records
     */
    public function update(array $data): bool
    {
        $setParts = [];
        foreach (array_keys($data) as $column) {
            $setParts[] = "$column = ?";
        }
        
        $sql = "UPDATE {$this->table} SET " . implode(', ', $setParts);
        
        if (!empty($this->where)) {
            $sql .= ' WHERE ' . $this->buildWhereClause();
        }
        
        $stmt = $this->connection->prepare($sql);
        
        $i = 1;
        foreach ($data as $value) {
            $stmt->bindValue($i++, $value);
        }
        
        $this->bindWhereParameters($stmt, $i);
        
        return $stmt->execute() !== false;
    }

    /**
     * Delete records
     */
    public function delete(): bool
    {
        $sql = "DELETE FROM {$this->table}";
        
        if (!empty($this->where)) {
            $sql .= ' WHERE ' . $this->buildWhereClause();
        }
        
        $stmt = $this->connection->prepare($sql);
        $this->bindWhereParameters($stmt, 1);
        
        return $stmt->execute() !== false;
    }

    /**
     * Build select query
     */
    private function buildSelectQuery(): string
    {
        $sql = 'SELECT ' . implode(', ', $this->select) . " FROM {$this->table}";
        
        // Add joins
        foreach ($this->joins as $join) {
            $sql .= " {$join['type']} JOIN {$join['table']} ON {$join['first']} {$join['operator']} {$join['second']}";
        }
        
        // Add where clause
        if (!empty($this->where)) {
            $sql .= ' WHERE ' . $this->buildWhereClause();
        }
        
        // Add group by
        if (!empty($this->groupBy)) {
            $sql .= ' GROUP BY ' . implode(', ', $this->groupBy);
        }
        
        // Add order by
        if (!empty($this->orderBy)) {
            $orderParts = [];
            foreach ($this->orderBy as $order) {
                $orderParts[] = "{$order['column']} {$order['direction']}";
            }
            $sql .= ' ORDER BY ' . implode(', ', $orderParts);
        }
        
        // Add limit and offset
        if ($this->limit !== null) {
            $sql .= " LIMIT {$this->limit}";
            if ($this->offset !== null) {
                $sql .= " OFFSET {$this->offset}";
            }
        }
        
        return $sql;
    }

    /**
     * Build where clause
     */
    private function buildWhereClause(): string
    {
        $parts = [];
        $first = true;
        
        foreach ($this->where as $condition) {
            $connector = $first ? '' : " {$condition['type']} ";
            $first = false;
            
            if ($condition['operator'] === 'IN') {
                $placeholders = str_repeat('?,', count($condition['value']) - 1) . '?';
                $parts[] = $connector . "{$condition['column']} IN ($placeholders)";
            } else {
                $parts[] = $connector . "{$condition['column']} {$condition['operator']} ?";
            }
        }
        
        return implode('', $parts);
    }

    /**
     * Bind parameters to statement
     */
    private function bindParameters($stmt): void
    {
        $this->bindWhereParameters($stmt, 1);
    }

    /**
     * Bind where parameters to statement
     */
    private function bindWhereParameters($stmt, int $startIndex): void
    {
        $index = $startIndex;
        
        foreach ($this->where as $condition) {
            if ($condition['operator'] === 'IN') {
                foreach ($condition['value'] as $value) {
                    $stmt->bindValue($index++, $value);
                }
            } else {
                $stmt->bindValue($index++, $condition['value']);
            }
        }
    }
}
