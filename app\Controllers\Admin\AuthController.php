<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Core\Http\Request;
use App\Core\Http\Response;

/**
 * Admin Authentication Controller
 * 
 * Handles admin login/logout functionality
 */
class AuthController extends BaseController
{
    /**
     * Show login form
     */
    public function showLogin(Request $request): Response
    {
        try {
            $this->ensureSession();

            // Redirect if already logged in
            if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
                $router = \App\Core\Application::getInstance()->getRouter();
                return Response::redirect($router->url('/admin'));
            }

            if ($request->isAjax()) {
                return $this->success(['show_login' => true]);
            }

            $router = \App\Core\Application::getInstance()->getRouter();
            return $this->render('admin.login', [
                'base_url' => $router->url('')
            ], '');
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Handle login
     */
    public function login(Request $request): Response
    {
        try {
            $data = $this->validate($request, [
                'username' => 'required',
                'password' => 'required'
            ]);

            // Get tenant data for admin credentials
            $tenantData = $this->getTenantData();
            $adminCredentials = $this->getAdminCredentials($tenantData);

            if ($this->validateCredentials($data['username'], $data['password'], $adminCredentials)) {
                $this->ensureSession();
                $_SESSION['admin_logged_in'] = true;
                $_SESSION['admin_user'] = [
                    'username' => $data['username'],
                    'tenant_id' => $this->getCurrentTenant(),
                    'login_time' => time()
                ];

                $router = \App\Core\Application::getInstance()->getRouter();
                if ($request->isAjax()) {
                    return $this->success(['redirect' => $router->url('/admin')]);
                }

                return Response::redirect($router->url('/admin'));
            } else {
                if ($request->isAjax()) {
                    return $this->error('Invalid username or password', 401);
                }

                $router = \App\Core\Application::getInstance()->getRouter();
                return $this->render('admin.login', [
                    'error' => 'Invalid username or password',
                    'username' => $data['username'],
                    'base_url' => $router->url('')
                ], '');
            }
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Handle logout
     */
    public function logout(Request $request): Response
    {
        try {
            $this->ensureSession();
            session_destroy();

            $router = \App\Core\Application::getInstance()->getRouter();
            if ($request->isAjax()) {
                return $this->success(['redirect' => $router->url('/admin/login')]);
            }

            return Response::redirect($router->url('/admin/login'));
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Check authentication status
     */
    public function check(Request $request): Response
    {
        try {
            $this->ensureSession();
            $isAuthenticated = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;

            return $this->success([
                'authenticated' => $isAuthenticated,
                'user' => $isAuthenticated ? $_SESSION['admin_user'] : null
            ]);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Change password
     */
    public function changePassword(Request $request): Response
    {
        try {
            $this->requireAuth();

            $data = $this->validate($request, [
                'current_password' => 'required',
                'new_password' => 'required|min:6',
                'confirm_password' => 'required'
            ]);

            if ($data['new_password'] !== $data['confirm_password']) {
                return $this->validationError(['confirm_password' => ['Passwords do not match']]);
            }

            // Get current admin credentials
            $tenantData = $this->getTenantData();
            $adminCredentials = $this->getAdminCredentials($tenantData);
            $currentUser = $this->getCurrentAdmin();

            // Verify current password
            if (!$this->validateCredentials($currentUser['username'], $data['current_password'], $adminCredentials)) {
                return $this->error('Current password is incorrect', 400);
            }

            // Update password in tenant settings
            $this->updateAdminPassword($currentUser['username'], $data['new_password']);

            return $this->success(null, 'Password changed successfully');
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get admin credentials from tenant data
     */
    private function getAdminCredentials(?array $tenantData): array
    {
        if (!$tenantData) {
            // Default credentials for realma tenant
            return [
                'admin' => password_hash('admin123', PASSWORD_DEFAULT)
            ];
        }

        $settings = $tenantData['settings'] ?? [];
        return $settings['admin_credentials'] ?? [
            'admin' => password_hash('admin123', PASSWORD_DEFAULT)
        ];
    }

    /**
     * Validate credentials
     */
    private function validateCredentials(string $username, string $password, array $credentials): bool
    {
        if (!isset($credentials[$username])) {
            return false;
        }

        $hashedPassword = $credentials[$username];

        // Check if password is already hashed
        if (password_get_info($hashedPassword)['algo'] !== null) {
            return password_verify($password, $hashedPassword);
        }

        // Legacy plain text password (for backward compatibility)
        return $password === $hashedPassword;
    }

    /**
     * Update admin password
     */
    private function updateAdminPassword(string $username, string $newPassword): void
    {
        $tenantManager = $this->app->getTenant();
        $tenantData = $tenantManager->getTenantData();

        if (!$tenantData) {
            return;
        }

        $settings = $tenantData['settings'] ?? [];
        $adminCredentials = $settings['admin_credentials'] ?? [];

        $adminCredentials[$username] = password_hash($newPassword, PASSWORD_DEFAULT);
        $settings['admin_credentials'] = $adminCredentials;

        // Update tenant settings
        $tenantManager->updateTenantSettings($settings);
    }

    /**
     * Get session info
     */
    public function sessionInfo(Request $request): Response
    {
        try {
            $this->requireAuth();

            $user = $this->getCurrentAdmin();
            $sessionDuration = time() - $user['login_time'];

            return $this->success([
                'user' => $user,
                'session_duration' => $sessionDuration,
                'tenant' => $this->getTenantData()
            ]);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Extend session
     */
    public function extendSession(Request $request): Response
    {
        try {
            $this->requireAuth();

            $this->ensureSession();
            $_SESSION['admin_user']['last_activity'] = time();

            return $this->success(null, 'Session extended');
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }
}
