<?php

namespace App\Models;

use App\Core\Model\BaseModel;

/**
 * Customer Model
 * 
 * Represents a customer in the booking system
 */
class Customer extends BaseModel
{
    protected string $table = 'customers';
    
    protected array $fillable = [
        'name',
        'email',
        'mobile',
        'address',
        'date_of_birth',
        'notes',
        'preferred_contact',
        'preferred_language',
        'avatar_url'
    ];

    protected array $hidden = [
        'user_hash'
    ];

    protected array $casts = [
        'total_reservations' => 'int',
        'total_spent' => 'float',
        'created_at' => 'string',
        'last_visit' => 'string'
    ];

    /**
     * Generate customer ID
     */
    protected function generateId(): string
    {
        return 'CUST' . strtoupper(uniqid());
    }

    /**
     * Get customer by email
     */
    public static function findByEmail(string $email): ?self
    {
        $instance = new static();
        $result = $instance->newQuery()
            ->where('email', '=', $email)
            ->first();
            
        if ($result) {
            $model = new static($result);
            $model->exists = true;
            $model->syncOriginal();
            return $model;
        }
        
        return null;
    }

    /**
     * Get customer reservations
     */
    public function reservations(): array
    {
        return Reservation::where('customer_id', $this->id)->get();
    }

    /**
     * Get customer's total spent amount
     */
    public function getTotalSpent(): float
    {
        $reservations = $this->reservations();
        $total = 0;
        
        foreach ($reservations as $reservation) {
            if ($reservation->status === 'completed') {
                $total += $reservation->price;
            }
        }
        
        return $total;
    }

    /**
     * Get customer's total reservations count
     */
    public function getTotalReservations(): int
    {
        return count($this->reservations());
    }

    /**
     * Update customer statistics
     */
    public function updateStatistics(): void
    {
        $this->total_spent = $this->getTotalSpent();
        $this->total_reservations = $this->getTotalReservations();
        
        // Update last visit
        $lastReservation = $this->getLastCompletedReservation();
        if ($lastReservation) {
            $this->last_visit = $lastReservation->date;
        }
        
        $this->save();
    }

    /**
     * Get last completed reservation
     */
    private function getLastCompletedReservation(): ?Reservation
    {
        $reservations = $this->reservations();
        $completed = array_filter($reservations, function($r) {
            return $r->status === 'completed';
        });
        
        if (empty($completed)) {
            return null;
        }
        
        // Sort by date descending
        usort($completed, function($a, $b) {
            return strtotime($b->date) - strtotime($a->date);
        });
        
        return $completed[0];
    }

    /**
     * Generate user hash for customer
     */
    public function generateUserHash(): string
    {
        return hash('sha256', $this->email . $this->created_at . uniqid());
    }

    /**
     * Validate email format
     */
    public static function validateEmail(string $email): bool
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * Validate mobile format
     */
    public static function validateMobile(string $mobile): bool
    {
        // Greek mobile format: +30 6XX XXX XXXX or 6XX XXX XXXX
        $pattern = '/^(\+30\s?)?6\d{8}$/';
        return preg_match($pattern, str_replace([' ', '-'], '', $mobile));
    }

    /**
     * Format mobile number
     */
    public static function formatMobile(string $mobile): string
    {
        $clean = str_replace([' ', '-', '+'], '', $mobile);
        
        if (strlen($clean) === 10 && substr($clean, 0, 1) === '6') {
            return '+30 ' . substr($clean, 0, 3) . ' ' . substr($clean, 3, 3) . ' ' . substr($clean, 6);
        }
        
        return $mobile;
    }

    /**
     * Get customer's preferred language
     */
    public function getPreferredLanguage(): string
    {
        return $this->preferred_language ?? 'el';
    }

    /**
     * Check if customer prefers email contact
     */
    public function prefersEmail(): bool
    {
        return $this->preferred_contact === 'email';
    }

    /**
     * Check if customer prefers SMS contact
     */
    public function prefersSms(): bool
    {
        return $this->preferred_contact === 'sms';
    }

    /**
     * Get customer's full contact info
     */
    public function getContactInfo(): array
    {
        return [
            'name' => $this->name,
            'email' => $this->email,
            'mobile' => $this->mobile,
            'preferred_contact' => $this->preferred_contact,
            'preferred_language' => $this->preferred_language
        ];
    }

    /**
     * Search customers
     */
    public static function search(string $term, int $limit = 20, int $offset = 0): array
    {
        $instance = new static();
        $query = $instance->newQuery();
        
        if (!empty($term)) {
            $query->where('name', 'LIKE', "%$term%")
                  ->orWhere('email', 'LIKE', "%$term%")
                  ->orWhere('mobile', 'LIKE', "%$term%");
        }
        
        return $query->orderBy('name')
                    ->limit($limit)
                    ->offset($offset)
                    ->get();
    }

    /**
     * Get customers count for search
     */
    public static function searchCount(string $term = ''): int
    {
        $instance = new static();
        $query = $instance->newQuery();
        
        if (!empty($term)) {
            $query->where('name', 'LIKE', "%$term%")
                  ->orWhere('email', 'LIKE', "%$term%")
                  ->orWhere('mobile', 'LIKE', "%$term%");
        }
        
        return $query->count();
    }

    /**
     * Before save hook
     */
    public function save(): bool
    {
        // Generate user hash if not exists
        if (!$this->user_hash) {
            $this->user_hash = $this->generateUserHash();
        }
        
        // Format mobile number
        if ($this->mobile) {
            $this->mobile = self::formatMobile($this->mobile);
        }
        
        return parent::save();
    }
}
