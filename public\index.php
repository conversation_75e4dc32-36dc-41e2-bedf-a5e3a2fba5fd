<?php

/**
 * Application Entry Point
 * 
 * This is the main entry point for the booking system.
 * All requests are routed through this file.
 */

// Set error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application root
define('APP_ROOT', dirname(__DIR__));

// Autoloader
require_once APP_ROOT . '/vendor/autoload.php';

// Start the application
try {
    $app = \App\Core\Application::getInstance();
    $app->initialize();
    $app->run();
} catch (Exception $e) {
    // Handle fatal errors
    http_response_code(500);
    
    if (php_sapi_name() === 'cli') {
        echo "Error: " . $e->getMessage() . "\n";
        echo $e->getTraceAsString() . "\n";
    } else {
        header('Content-Type: application/json');
        echo json_encode([
            'error' => 'Internal Server Error',
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
    }
    
    exit(1);
}
