<?php

namespace App\Controllers\Api;

use App\Controllers\BaseController;
use App\Core\Http\Request;
use App\Core\Http\Response;
use App\Models\Reservation;
use App\Models\Customer;
use App\Models\Service;
use App\Models\Employee;

/**
 * Reservation API Controller
 * 
 * Handles reservation-related API endpoints
 */
class ReservationController extends BaseController
{
    /**
     * Get all reservations
     */
    public function index(Request $request): Response
    {
        try {
            $pagination = $this->getPaginationParams($request);
            $filters = $this->getFilters($request, [
                'customer_name', 'service', 'status', 'date_from', 'date_to', 'employee_id'
            ]);
            
            $reservations = Reservation::search($filters, $pagination['limit'], $pagination['offset']);
            $total = Reservation::searchCount($filters);
            
            // Enrich with customer and employee data
            foreach ($reservations as &$reservation) {
                $customer = $reservation->customer();
                $employee = $reservation->employee();
                
                $reservation = $this->formatModel($reservation);
                $reservation['customer'] = $customer ? $this->formatModel($customer) : null;
                $reservation['employee'] = $employee ? $this->formatModel($employee) : null;
            }
            
            return $this->paginatedResponse(
                $reservations,
                $total,
                $pagination['page'],
                $pagination['limit']
            );
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get reservation by ID
     */
    public function show(Request $request, array $params): Response
    {
        try {
            $reservation = Reservation::find($params['id']);
            
            if (!$reservation) {
                return $this->notFound('Reservation not found');
            }
            
            $data = $this->formatModel($reservation);
            $data['customer'] = $this->formatModel($reservation->customer());
            $data['employee'] = $reservation->employee() ? $this->formatModel($reservation->employee()) : null;
            $data['service_details'] = $reservation->service() ? $this->formatModel($reservation->service()) : null;
            
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Create new reservation
     */
    public function store(Request $request): Response
    {
        try {
            $data = $this->validate($request, [
                'customer_id' => 'required',
                'service' => 'required',
                'date' => 'required',
                'time' => 'required',
                'duration' => 'required',
                'price' => 'required'
            ]);

            // Validate customer exists
            $customer = Customer::find($data['customer_id']);
            if (!$customer) {
                return $this->error('Customer not found', 404);
            }

            // Validate service exists
            $service = Service::findByName($data['service']);
            if (!$service) {
                return $this->error('Service not found', 404);
            }

            // Validate employee if provided
            if (!empty($data['employee_id'])) {
                $employee = Employee::find($data['employee_id']);
                if (!$employee || !$employee->isActive()) {
                    return $this->error('Employee not found or inactive', 404);
                }
            }

            // Check for conflicts
            $conflicts = $this->checkConflicts($data);
            if (!empty($conflicts)) {
                return $this->error('Time slot conflicts with existing reservation', 409);
            }

            $data['status'] = Reservation::STATUS_PENDING;
            $reservation = Reservation::create($data);
            
            return $this->created($reservation, 'Reservation created successfully');
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Update reservation
     */
    public function update(Request $request, array $params): Response
    {
        try {
            $reservation = Reservation::find($params['id']);
            
            if (!$reservation) {
                return $this->notFound('Reservation not found');
            }

            $data = $request->only([
                'service', 'date', 'time', 'duration', 'price', 'employee_id', 'status'
            ]);

            // Validate service if changed
            if (isset($data['service'])) {
                $service = Service::findByName($data['service']);
                if (!$service) {
                    return $this->error('Service not found', 404);
                }
            }

            // Validate employee if changed
            if (isset($data['employee_id']) && !empty($data['employee_id'])) {
                $employee = Employee::find($data['employee_id']);
                if (!$employee || !$employee->isActive()) {
                    return $this->error('Employee not found or inactive', 404);
                }
            }

            // Check for conflicts if time/date changed
            if (isset($data['date']) || isset($data['time']) || isset($data['duration'])) {
                $checkData = array_merge($reservation->toArray(), $data);
                $conflicts = $this->checkConflicts($checkData, $reservation->id);
                if (!empty($conflicts)) {
                    return $this->error('Time slot conflicts with existing reservation', 409);
                }
            }

            foreach ($data as $key => $value) {
                $reservation->$key = $value;
            }

            $reservation->save();
            
            return $this->updated($reservation, 'Reservation updated successfully');
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Delete reservation
     */
    public function destroy(Request $request, array $params): Response
    {
        try {
            $reservation = Reservation::find($params['id']);
            
            if (!$reservation) {
                return $this->notFound('Reservation not found');
            }

            $reservation->delete();
            
            return $this->deleted('Reservation deleted successfully');
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Confirm reservation
     */
    public function confirm(Request $request, array $params): Response
    {
        try {
            $reservation = Reservation::find($params['id']);
            
            if (!$reservation) {
                return $this->notFound('Reservation not found');
            }

            if (!$reservation->isPending()) {
                return $this->error('Only pending reservations can be confirmed', 400);
            }

            $reservation->confirm();
            
            return $this->success($reservation, 'Reservation confirmed successfully');
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Complete reservation
     */
    public function complete(Request $request, array $params): Response
    {
        try {
            $reservation = Reservation::find($params['id']);
            
            if (!$reservation) {
                return $this->notFound('Reservation not found');
            }

            if (!$reservation->isConfirmed()) {
                return $this->error('Only confirmed reservations can be completed', 400);
            }

            $reservation->complete();
            
            return $this->success($reservation, 'Reservation completed successfully');
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Cancel reservation
     */
    public function cancel(Request $request, array $params): Response
    {
        try {
            $reservation = Reservation::find($params['id']);
            
            if (!$reservation) {
                return $this->notFound('Reservation not found');
            }

            if ($reservation->isCompleted()) {
                return $this->error('Completed reservations cannot be cancelled', 400);
            }

            $reservation->cancel();
            
            return $this->success($reservation, 'Reservation cancelled successfully');
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Mark reservation as no show
     */
    public function noShow(Request $request, array $params): Response
    {
        try {
            $reservation = Reservation::find($params['id']);
            
            if (!$reservation) {
                return $this->notFound('Reservation not found');
            }

            if (!$reservation->isConfirmed()) {
                return $this->error('Only confirmed reservations can be marked as no show', 400);
            }

            $reservation->markNoShow();
            
            return $this->success($reservation, 'Reservation marked as no show');
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get reservations for specific date
     */
    public function forDate(Request $request, array $params): Response
    {
        try {
            $date = $params['date'];
            
            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
                return $this->error('Invalid date format. Use YYYY-MM-DD', 400);
            }

            $reservations = Reservation::forDate($date);
            
            // Enrich with customer and employee data
            foreach ($reservations as &$reservation) {
                $customer = $reservation->customer();
                $employee = $reservation->employee();
                
                $reservation = $this->formatModel($reservation);
                $reservation['customer'] = $customer ? $this->formatModel($customer) : null;
                $reservation['employee'] = $employee ? $this->formatModel($employee) : null;
            }
            
            return $this->success($reservations);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get upcoming reservations
     */
    public function upcoming(Request $request): Response
    {
        try {
            $limit = min(50, max(1, (int)$request->input('limit', 10)));
            $reservations = Reservation::upcoming($limit);
            
            // Enrich with customer and employee data
            foreach ($reservations as &$reservation) {
                $customer = $reservation->customer();
                $employee = $reservation->employee();
                
                $reservation = $this->formatModel($reservation);
                $reservation['customer'] = $customer ? $this->formatModel($customer) : null;
                $reservation['employee'] = $employee ? $this->formatModel($employee) : null;
            }
            
            return $this->success($reservations);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get revenue statistics
     */
    public function revenue(Request $request): Response
    {
        try {
            $dateFrom = $request->input('date_from', date('Y-m-01'));
            $dateTo = $request->input('date_to', date('Y-m-t'));
            
            $stats = Reservation::getRevenueStats($dateFrom, $dateTo);
            
            return $this->success($stats);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Check for reservation conflicts
     */
    private function checkConflicts(array $data, ?string $excludeId = null): array
    {
        $date = $data['date'];
        $time = $data['time'];
        $duration = $data['duration'];
        $employeeId = $data['employee_id'] ?? null;

        $existingReservations = Reservation::forDate($date);
        
        $conflicts = [];
        foreach ($existingReservations as $existing) {
            if ($excludeId && $existing->id === $excludeId) {
                continue;
            }

            if ($existing->status === Reservation::STATUS_CANCELLED) {
                continue;
            }

            // Check employee conflict
            if ($employeeId && $existing->employee_id === $employeeId) {
                $newReservation = new Reservation($data);
                if ($newReservation->conflictsWith($existing)) {
                    $conflicts[] = $existing;
                }
            }
        }

        return $conflicts;
    }
}
