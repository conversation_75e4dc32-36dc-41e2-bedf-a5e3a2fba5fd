<?php

namespace App\Controllers\Api;

use App\Controllers\BaseController;
use App\Core\Http\Request;
use App\Core\Http\Response;
use App\Models\Service;
use App\Models\Employee;

/**
 * Service API Controller
 * 
 * Handles service-related API endpoints
 */
class ServiceController extends BaseController
{
    /**
     * Get all services
     */
    public function index(Request $request): Response
    {
        try {
            $pagination = $this->getPaginationParams($request);
            $search = $request->input('search', '');
            
            $services = Service::search($search, $pagination['limit'], $pagination['offset']);
            $total = Service::searchCount($search);
            
            // Enrich with employee data
            foreach ($services as &$service) {
                $employees = $service->employees();
                $service = $this->formatModel($service);
                $service['employees'] = $this->formatCollection($employees);
                $service['employee_count'] = count($employees);
            }
            
            return $this->paginatedResponse(
                $services,
                $total,
                $pagination['page'],
                $pagination['limit']
            );
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get service by ID
     */
    public function show(Request $request, array $params): Response
    {
        try {
            $service = Service::find($params['id']);
            
            if (!$service) {
                return $this->notFound('Service not found');
            }
            
            $data = $this->formatModel($service);
            $data['employees'] = $this->formatCollection($service->employees());
            $data['statistics'] = $service->getStatistics();
            
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Create new service
     */
    public function store(Request $request): Response
    {
        try {
            $data = $this->validate($request, [
                'name' => 'required',
                'duration' => 'required',
                'price' => 'required'
            ]);

            // Validate service data
            $errors = Service::validate($data);
            if (!empty($errors)) {
                return $this->validationError($errors);
            }

            // Check if service name already exists
            if (Service::findByName($data['name'])) {
                return $this->error('Service with this name already exists', 409);
            }

            $service = Service::create($data);
            
            return $this->created($service, 'Service created successfully');
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Update service
     */
    public function update(Request $request, array $params): Response
    {
        try {
            $service = Service::find($params['id']);
            
            if (!$service) {
                return $this->notFound('Service not found');
            }

            $data = $this->validate($request, [
                'name' => 'required',
                'duration' => 'required',
                'price' => 'required'
            ]);

            // Validate service data
            $errors = Service::validate($data);
            if (!empty($errors)) {
                return $this->validationError($errors);
            }

            // Check if service name already exists for another service
            $existingService = Service::findByName($data['name']);
            if ($existingService && $existingService->id !== $service->id) {
                return $this->error('Service with this name already exists', 409);
            }

            foreach ($data as $key => $value) {
                $service->$key = $value;
            }

            $service->save();
            
            return $this->updated($service, 'Service updated successfully');
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Delete service
     */
    public function destroy(Request $request, array $params): Response
    {
        try {
            $service = Service::find($params['id']);
            
            if (!$service) {
                return $this->notFound('Service not found');
            }

            try {
                $service->delete();
                return $this->deleted('Service deleted successfully');
            } catch (\Exception $e) {
                return $this->error($e->getMessage(), 409);
            }
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get service employees
     */
    public function employees(Request $request, array $params): Response
    {
        try {
            $service = Service::find($params['id']);
            
            if (!$service) {
                return $this->notFound('Service not found');
            }

            $employees = $service->employees();
            
            return $this->success($this->formatCollection($employees));
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Assign employees to service
     */
    public function assignEmployees(Request $request, array $params): Response
    {
        try {
            $service = Service::find($params['id']);
            
            if (!$service) {
                return $this->notFound('Service not found');
            }

            $data = $this->validate($request, [
                'employee_ids' => 'required'
            ]);

            $employeeIds = is_array($data['employee_ids']) ? $data['employee_ids'] : [$data['employee_ids']];

            // Validate all employees exist and are active
            foreach ($employeeIds as $employeeId) {
                $employee = Employee::find($employeeId);
                if (!$employee || !$employee->isActive()) {
                    return $this->error("Employee $employeeId not found or inactive", 404);
                }
            }

            $service->syncEmployees($employeeIds);
            
            return $this->success(null, 'Employees assigned to service successfully');
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Remove employee from service
     */
    public function removeEmployee(Request $request, array $params): Response
    {
        try {
            $service = Service::find($params['id']);
            
            if (!$service) {
                return $this->notFound('Service not found');
            }

            $employeeId = $params['employee_id'];
            $employee = Employee::find($employeeId);
            
            if (!$employee) {
                return $this->notFound('Employee not found');
            }

            $service->removeEmployee($employeeId);
            
            return $this->success(null, 'Employee removed from service successfully');
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get service statistics
     */
    public function statistics(Request $request, array $params): Response
    {
        try {
            $service = Service::find($params['id']);
            
            if (!$service) {
                return $this->notFound('Service not found');
            }

            $stats = $service->getStatistics();
            
            return $this->success($stats);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get popular services
     */
    public function popular(Request $request): Response
    {
        try {
            $limit = min(10, max(1, (int)$request->input('limit', 5)));
            $services = Service::getPopular($limit);
            
            return $this->success($this->formatCollection($services));
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get service options for forms
     */
    public function options(Request $request): Response
    {
        try {
            $options = Service::getOptions();
            
            return $this->success($options);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get services for specific employee
     */
    public function forEmployee(Request $request, array $params): Response
    {
        try {
            $employeeId = $params['employee_id'];
            $employee = Employee::find($employeeId);
            
            if (!$employee) {
                return $this->notFound('Employee not found');
            }

            $services = $employee->services();
            
            return $this->success($this->formatCollection($services));
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }
}
