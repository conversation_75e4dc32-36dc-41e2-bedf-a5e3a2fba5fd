<!DOCTYPE html>
<html lang="el">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - <?= $business_info['business_name'] ?? 'Booking System' ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            margin: 0.25rem 0;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }
        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        .stat-card.blue {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .stat-card.green {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .stat-card.orange {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-3">
                <div class="text-center mb-4">
                    <h4 class="text-white">
                        <i class="fas fa-calendar-check me-2"></i>
                        Admin Panel
                    </h4>
                </div>
                
                <nav class="nav flex-column">
                    <a class="nav-link active" href="#dashboard">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        Dashboard
                    </a>
                    <a class="nav-link" href="#reservations">
                        <i class="fas fa-calendar-alt me-2"></i>
                        Κρατήσεις
                    </a>
                    <a class="nav-link" href="#customers">
                        <i class="fas fa-users me-2"></i>
                        Πελάτες
                    </a>
                    <a class="nav-link" href="#services">
                        <i class="fas fa-cut me-2"></i>
                        Υπηρεσίες
                    </a>
                    <a class="nav-link" href="#employees">
                        <i class="fas fa-user-tie me-2"></i>
                        Εργαζόμενοι
                    </a>
                    <a class="nav-link" href="#settings">
                        <i class="fas fa-cog me-2"></i>
                        Ρυθμίσεις
                    </a>
                    <hr class="text-white-50">
                    <a class="nav-link" href="<?= rtrim($base_url ?? '', '/') ?>/admin/logout" onclick="return confirm('Είστε σίγουροι ότι θέλετε να αποσυνδεθείτε;')">
                        <i class="fas fa-sign-out-alt me-2"></i>
                        Αποσύνδεση
                    </a>
                </nav>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 p-4">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>Dashboard</h2>
                    <div class="text-muted">
                        <i class="fas fa-user me-1"></i>
                        Καλώς ήρθατε, <?= $admin_user['username'] ?? 'Admin' ?>
                    </div>
                </div>
                
                <!-- Stats Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card blue">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 id="total-reservations">0</h3>
                                    <p class="mb-0">Συνολικές Κρατήσεις</p>
                                </div>
                                <i class="fas fa-calendar-check fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card green">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 id="today-reservations">0</h3>
                                    <p class="mb-0">Σημερινές Κρατήσεις</p>
                                </div>
                                <i class="fas fa-calendar-day fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card orange">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 id="total-customers">0</h3>
                                    <p class="mb-0">Συνολικοί Πελάτες</p>
                                </div>
                                <i class="fas fa-users fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 id="monthly-revenue">€0</h3>
                                    <p class="mb-0">Μηνιαία Έσοδα</p>
                                </div>
                                <i class="fas fa-euro-sign fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Content Sections -->
                <div id="dashboard" class="content-section">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-calendar-alt me-2"></i>Πρόσφατες Κρατήσεις</h5>
                                </div>
                                <div class="card-body">
                                    <div id="recent-reservations">
                                        <div class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Φόρτωση...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-clock me-2"></i>Σημερινό Πρόγραμμα</h5>
                                </div>
                                <div class="card-body">
                                    <div id="today-schedule">
                                        <div class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Φόρτωση...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Other sections will be loaded dynamically -->
                <div id="reservations" class="content-section d-none">
                    <h3>Διαχείριση Κρατήσεων</h3>
                    <div id="reservations-content"></div>
                </div>
                
                <div id="customers" class="content-section d-none">
                    <h3>Διαχείριση Πελατών</h3>
                    <div id="customers-content"></div>
                </div>
                
                <div id="services" class="content-section d-none">
                    <h3>Διαχείριση Υπηρεσιών</h3>
                    <div id="services-content"></div>
                </div>
                
                <div id="employees" class="content-section d-none">
                    <h3>Διαχείριση Εργαζομένων</h3>
                    <div id="employees-content"></div>
                </div>
                
                <div id="settings" class="content-section d-none">
                    <h3>Ρυθμίσεις Συστήματος</h3>
                    <div id="settings-content"></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        const baseUrl = '<?= rtrim($base_url ?? '', '/') ?>';
        
        // Navigation handling
        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                if (this.getAttribute('href').startsWith('#')) {
                    e.preventDefault();
                    const target = this.getAttribute('href').substring(1);
                    showSection(target);
                    
                    // Update active state
                    document.querySelectorAll('.sidebar .nav-link').forEach(l => l.classList.remove('active'));
                    this.classList.add('active');
                }
            });
        });
        
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.add('d-none');
            });
            
            // Show target section
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.classList.remove('d-none');
            }
            
            // Load content if needed
            loadSectionContent(sectionId);
        }
        
        function loadSectionContent(sectionId) {
            switch(sectionId) {
                case 'dashboard':
                    loadDashboardData();
                    break;
                case 'reservations':
                    loadReservations();
                    break;
                case 'customers':
                    loadCustomers();
                    break;
                case 'services':
                    loadServices();
                    break;
                case 'employees':
                    loadEmployees();
                    break;
                case 'settings':
                    loadSettings();
                    break;
            }
        }
        
        function loadDashboardData() {
            // Load statistics
            fetch(`${baseUrl}/api/admin/stats`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('total-reservations').textContent = data.data.total_reservations || 0;
                        document.getElementById('today-reservations').textContent = data.data.today_reservations || 0;
                        document.getElementById('total-customers').textContent = data.data.total_customers || 0;
                        document.getElementById('monthly-revenue').textContent = '€' + (data.data.monthly_revenue || 0);
                    }
                })
                .catch(error => console.error('Error loading stats:', error));
            
            // Load recent reservations
            loadRecentReservations();
            loadTodaySchedule();
        }
        
        function loadRecentReservations() {
            fetch(`${baseUrl}/api/reservations?limit=5`)
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('recent-reservations');
                    if (data.success && data.data.length > 0) {
                        container.innerHTML = data.data.map(reservation => `
                            <div class="border-bottom py-2">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <strong>${reservation.customer_name || 'Άγνωστος'}</strong>
                                        <br>
                                        <small class="text-muted">${reservation.service} - ${reservation.date} ${reservation.time}</small>
                                    </div>
                                    <span class="badge bg-${getStatusColor(reservation.status)}">${getStatusText(reservation.status)}</span>
                                </div>
                            </div>
                        `).join('');
                    } else {
                        container.innerHTML = '<p class="text-muted text-center">Δεν υπάρχουν πρόσφατες κρατήσεις</p>';
                    }
                })
                .catch(error => {
                    document.getElementById('recent-reservations').innerHTML = '<p class="text-danger text-center">Σφάλμα φόρτωσης</p>';
                });
        }
        
        function loadTodaySchedule() {
            const today = new Date().toISOString().split('T')[0];
            fetch(`${baseUrl}/api/reservations?date=${today}`)
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('today-schedule');
                    if (data.success && data.data.length > 0) {
                        container.innerHTML = data.data.map(reservation => `
                            <div class="mb-2 p-2 border rounded">
                                <div class="fw-bold">${reservation.time}</div>
                                <div class="small">${reservation.service}</div>
                                <div class="small text-muted">${reservation.customer_name || 'Άγνωστος'}</div>
                            </div>
                        `).join('');
                    } else {
                        container.innerHTML = '<p class="text-muted text-center">Δεν υπάρχουν κρατήσεις σήμερα</p>';
                    }
                })
                .catch(error => {
                    document.getElementById('today-schedule').innerHTML = '<p class="text-danger text-center">Σφάλμα φόρτωσης</p>';
                });
        }
        
        function getStatusColor(status) {
            switch(status) {
                case 'confirmed': return 'success';
                case 'pending': return 'warning';
                case 'cancelled': return 'danger';
                case 'completed': return 'info';
                default: return 'secondary';
            }
        }
        
        function getStatusText(status) {
            switch(status) {
                case 'confirmed': return 'Επιβεβαιωμένη';
                case 'pending': return 'Εκκρεμής';
                case 'cancelled': return 'Ακυρωμένη';
                case 'completed': return 'Ολοκληρωμένη';
                default: return 'Άγνωστη';
            }
        }
        
        // Placeholder functions for other sections
        function loadReservations() {
            document.getElementById('reservations-content').innerHTML = '<p>Φόρτωση κρατήσεων...</p>';
        }
        
        function loadCustomers() {
            document.getElementById('customers-content').innerHTML = '<p>Φόρτωση πελατών...</p>';
        }
        
        function loadServices() {
            document.getElementById('services-content').innerHTML = '<p>Φόρτωση υπηρεσιών...</p>';
        }
        
        function loadEmployees() {
            document.getElementById('employees-content').innerHTML = '<p>Φόρτωση εργαζομένων...</p>';
        }
        
        function loadSettings() {
            document.getElementById('settings-content').innerHTML = '<p>Φόρτωση ρυθμίσεων...</p>';
        }
        
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboardData();
        });
    </script>
</body>
</html>
