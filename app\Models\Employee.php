<?php

namespace App\Models;

use App\Core\Model\BaseModel;

/**
 * Employee Model
 * 
 * Represents an employee in the booking system
 */
class Employee extends BaseModel
{
    protected string $table = 'employees';
    
    protected array $fillable = [
        'name',
        'email',
        'phone',
        'working_hours',
        'status'
    ];

    protected array $casts = [
        'working_hours' => 'json',
        'created_at' => 'string'
    ];

    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';

    /**
     * Generate employee ID
     */
    protected function generateId(): string
    {
        return 'EMP' . strtoupper(uniqid());
    }

    /**
     * Get services assigned to this employee
     */
    public function services(): array
    {
        $instance = new Service();
        $results = $instance->newQuery()
            ->join('employee_services', 'services.id', '=', 'employee_services.service_id')
            ->where('employee_services.employee_id', '=', $this->id)
            ->select(['services.*'])
            ->get();

        $services = [];
        foreach ($results as $result) {
            $model = new Service($result);
            $model->exists = true;
            $model->syncOriginal();
            $services[] = $model;
        }

        return $services;
    }

    /**
     * Get employee reservations
     */
    public function reservations(): array
    {
        $instance = new Reservation();
        $results = $instance->newQuery()
            ->where('employee_id', '=', $this->id)
            ->orderBy('date', 'DESC')
            ->orderBy('time', 'DESC')
            ->get();

        $reservations = [];
        foreach ($results as $result) {
            $model = new Reservation($result);
            $model->exists = true;
            $model->syncOriginal();
            $reservations[] = $model;
        }

        return $reservations;
    }

    /**
     * Check if employee is active
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    /**
     * Activate employee
     */
    public function activate(): bool
    {
        $this->status = self::STATUS_ACTIVE;
        return $this->save();
    }

    /**
     * Deactivate employee
     */
    public function deactivate(): bool
    {
        $this->status = self::STATUS_INACTIVE;
        return $this->save();
    }

    /**
     * Get working hours for a specific day
     */
    public function getWorkingHours(string $day): array
    {
        $workingHours = $this->working_hours ?? [];
        return $workingHours[$day] ?? [];
    }

    /**
     * Set working hours for a specific day
     */
    public function setWorkingHours(string $day, array $hours): void
    {
        $workingHours = $this->working_hours ?? [];
        $workingHours[$day] = $hours;
        $this->working_hours = $workingHours;
    }

    /**
     * Check if employee is working on a specific day and time
     */
    public function isWorkingAt(string $day, string $time): bool
    {
        $hours = $this->getWorkingHours($day);
        
        if (empty($hours)) {
            return false;
        }

        $timeMinutes = $this->timeToMinutes($time);
        
        foreach ($hours as $period) {
            $startMinutes = $this->timeToMinutes($period['start']);
            $endMinutes = $this->timeToMinutes($period['end']);
            
            if ($timeMinutes >= $startMinutes && $timeMinutes < $endMinutes) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Convert time string to minutes
     */
    private function timeToMinutes(string $time): int
    {
        [$hours, $minutes] = explode(':', $time);
        return (int)$hours * 60 + (int)$minutes;
    }

    /**
     * Get employee's availability for a specific date
     */
    public function getAvailabilityForDate(string $date): array
    {
        $dayOfWeek = date('l', strtotime($date));
        $workingHours = $this->getWorkingHours($dayOfWeek);
        
        if (empty($workingHours)) {
            return [];
        }

        // Get existing reservations for this date
        $reservations = Reservation::forEmployeeAndDate($this->id, $date);
        
        $availability = [];
        foreach ($workingHours as $period) {
            $start = $period['start'];
            $end = $period['end'];
            
            // Generate time slots (15-minute intervals)
            $current = strtotime($start);
            $endTime = strtotime($end);
            
            while ($current < $endTime) {
                $timeSlot = date('H:i', $current);
                $isAvailable = true;
                
                // Check if this time slot conflicts with any reservation
                foreach ($reservations as $reservation) {
                    $resStart = strtotime($reservation->time);
                    $resEnd = $resStart + ($reservation->duration * 60);
                    
                    if ($current >= $resStart && $current < $resEnd) {
                        $isAvailable = false;
                        break;
                    }
                }
                
                if ($isAvailable) {
                    $availability[] = $timeSlot;
                }
                
                $current += 15 * 60; // 15 minutes
            }
        }
        
        return $availability;
    }

    /**
     * Get employee statistics
     */
    public function getStatistics(): array
    {
        $reservations = $this->reservations();
        $completed = array_filter($reservations, function($r) {
            return $r->status === Reservation::STATUS_COMPLETED;
        });

        $totalRevenue = array_sum(array_map(function($r) {
            return $r->price;
        }, $completed));

        return [
            'total_reservations' => count($reservations),
            'completed_reservations' => count($completed),
            'total_revenue' => $totalRevenue,
            'services_count' => count($this->services())
        ];
    }

    /**
     * Search employees
     */
    public static function search(string $term = '', int $limit = 20, int $offset = 0): array
    {
        $instance = new static();
        $query = $instance->newQuery();
        
        if (!empty($term)) {
            $query->where('name', 'LIKE', "%$term%")
                  ->orWhere('email', 'LIKE', "%$term%")
                  ->orWhere('phone', 'LIKE', "%$term%");
        }
        
        $results = $query->orderBy('name')
                        ->limit($limit)
                        ->offset($offset)
                        ->get();

        $employees = [];
        foreach ($results as $result) {
            $model = new static($result);
            $model->exists = true;
            $model->syncOriginal();
            $employees[] = $model;
        }

        return $employees;
    }

    /**
     * Get search count
     */
    public static function searchCount(string $term = ''): int
    {
        $instance = new static();
        $query = $instance->newQuery();
        
        if (!empty($term)) {
            $query->where('name', 'LIKE', "%$term%")
                  ->orWhere('email', 'LIKE', "%$term%")
                  ->orWhere('phone', 'LIKE', "%$term%");
        }
        
        return $query->count();
    }

    /**
     * Get active employees
     */
    public static function getActive(): array
    {
        $instance = new static();
        $results = $instance->newQuery()
            ->where('status', '=', self::STATUS_ACTIVE)
            ->orderBy('name')
            ->get();

        $employees = [];
        foreach ($results as $result) {
            $model = new static($result);
            $model->exists = true;
            $model->syncOriginal();
            $employees[] = $model;
        }

        return $employees;
    }

    /**
     * Get employees for service
     */
    public static function forService(string $serviceId): array
    {
        $instance = new static();
        $results = $instance->newQuery()
            ->join('employee_services', 'employees.id', '=', 'employee_services.employee_id')
            ->where('employee_services.service_id', '=', $serviceId)
            ->where('employees.status', '=', self::STATUS_ACTIVE)
            ->select(['employees.*'])
            ->orderBy('employees.name')
            ->get();

        $employees = [];
        foreach ($results as $result) {
            $model = new static($result);
            $model->exists = true;
            $model->syncOriginal();
            $employees[] = $model;
        }

        return $employees;
    }

    /**
     * Assign service to employee
     */
    public function assignService(string $serviceId): bool
    {
        try {
            $conn = $this->newQuery()->getConnection();
            $stmt = $conn->prepare("INSERT OR IGNORE INTO employee_services (employee_id, service_id) VALUES (?, ?)");
            $stmt->bindValue(1, $this->id);
            $stmt->bindValue(2, $serviceId);
            return $stmt->execute() !== false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Remove service from employee
     */
    public function removeService(string $serviceId): bool
    {
        try {
            $conn = $this->newQuery()->getConnection();
            $stmt = $conn->prepare("DELETE FROM employee_services WHERE employee_id = ? AND service_id = ?");
            $stmt->bindValue(1, $this->id);
            $stmt->bindValue(2, $serviceId);
            return $stmt->execute() !== false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Sync services with employee
     */
    public function syncServices(array $serviceIds): bool
    {
        try {
            $conn = $this->newQuery()->getConnection();
            
            // Start transaction
            $conn->exec('BEGIN TRANSACTION');
            
            // Remove all existing assignments
            $stmt = $conn->prepare("DELETE FROM employee_services WHERE employee_id = ?");
            $stmt->bindValue(1, $this->id);
            $stmt->execute();
            
            // Add new assignments
            foreach ($serviceIds as $serviceId) {
                $stmt = $conn->prepare("INSERT INTO employee_services (employee_id, service_id) VALUES (?, ?)");
                $stmt->bindValue(1, $this->id);
                $stmt->bindValue(2, $serviceId);
                $stmt->execute();
            }
            
            $conn->exec('COMMIT');
            return true;
        } catch (\Exception $e) {
            $conn->exec('ROLLBACK');
            return false;
        }
    }

    /**
     * Get default working hours
     */
    public static function getDefaultWorkingHours(): array
    {
        return [
            'Monday' => [['start' => '09:00', 'end' => '17:00']],
            'Tuesday' => [['start' => '09:00', 'end' => '17:00']],
            'Wednesday' => [['start' => '09:00', 'end' => '17:00']],
            'Thursday' => [['start' => '09:00', 'end' => '17:00']],
            'Friday' => [['start' => '09:00', 'end' => '17:00']],
            'Saturday' => [],
            'Sunday' => []
        ];
    }

    /**
     * Validate employee data
     */
    public static function validate(array $data): array
    {
        $errors = [];

        if (empty($data['name'])) {
            $errors['name'] = 'Employee name is required';
        }

        if (!empty($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'Valid email address is required';
        }

        return $errors;
    }

    /**
     * Before save hook
     */
    public function save(): bool
    {
        // Set default working hours if not set
        if (!$this->working_hours) {
            $this->working_hours = self::getDefaultWorkingHours();
        }
        
        return parent::save();
    }

    /**
     * Before delete - check for dependencies
     */
    public function delete(): bool
    {
        // Check if employee has future reservations
        $futureReservations = array_filter($this->reservations(), function($r) {
            return strtotime($r->date) >= strtotime(date('Y-m-d')) && 
                   in_array($r->status, [Reservation::STATUS_PENDING, Reservation::STATUS_CONFIRMED]);
        });

        if (!empty($futureReservations)) {
            throw new \Exception('Cannot delete employee with future reservations');
        }

        // Remove service assignments
        try {
            $conn = $this->newQuery()->getConnection();
            $stmt = $conn->prepare("DELETE FROM employee_services WHERE employee_id = ?");
            $stmt->bindValue(1, $this->id);
            $stmt->execute();
        } catch (\Exception $e) {
            // Continue with deletion
        }

        return parent::delete();
    }
}
