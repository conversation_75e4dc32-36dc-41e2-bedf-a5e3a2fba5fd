<?php

namespace App\Models;

use App\Core\Model\BaseModel;

/**
 * Tenant Model
 * 
 * Represents a tenant in the multi-tenant system
 */
class Tenant extends BaseModel
{
    protected string $table = 'tenants';
    
    protected array $fillable = [
        'business_name',
        'domain',
        'subdomain',
        'plan',
        'status',
        'trial_ends_at',
        'billing_email',
        'settings'
    ];

    protected array $casts = [
        'settings' => 'json',
        'created_at' => 'string',
        'updated_at' => 'string',
        'trial_ends_at' => 'string'
    ];

    const STATUS_TRIAL = 'trial';
    const STATUS_ACTIVE = 'active';
    const STATUS_SUSPENDED = 'suspended';
    const STATUS_CANCELLED = 'cancelled';

    const PLAN_STARTER = 'starter';
    const PLAN_PROFESSIONAL = 'professional';
    const PLAN_PREMIUM = 'premium';

    /**
     * This model is not tenant-aware (it manages tenants)
     */
    public function isTenantAware(): bool
    {
        return false;
    }

    /**
     * Generate tenant ID
     */
    protected function generateId(): string
    {
        return 'tenant_' . uniqid();
    }

    /**
     * Find tenant by subdomain
     */
    public static function findBySubdomain(string $subdomain): ?self
    {
        $instance = new static();
        $result = $instance->newQuery()
            ->where('subdomain', '=', $subdomain)
            ->where('status', '=', self::STATUS_ACTIVE)
            ->first();
            
        if ($result) {
            $model = new static($result);
            $model->exists = true;
            $model->syncOriginal();
            return $model;
        }
        
        return null;
    }

    /**
     * Find tenant by domain
     */
    public static function findByDomain(string $domain): ?self
    {
        $instance = new static();
        $result = $instance->newQuery()
            ->where('domain', '=', $domain)
            ->where('status', '=', self::STATUS_ACTIVE)
            ->first();
            
        if ($result) {
            $model = new static($result);
            $model->exists = true;
            $model->syncOriginal();
            return $model;
        }
        
        return null;
    }

    /**
     * Check if tenant is active
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    /**
     * Check if tenant is on trial
     */
    public function isOnTrial(): bool
    {
        return $this->status === self::STATUS_TRIAL;
    }

    /**
     * Check if trial has expired
     */
    public function isTrialExpired(): bool
    {
        if (!$this->isOnTrial() || !$this->trial_ends_at) {
            return false;
        }
        
        return strtotime($this->trial_ends_at) < time();
    }

    /**
     * Get days remaining in trial
     */
    public function getTrialDaysRemaining(): int
    {
        if (!$this->isOnTrial() || !$this->trial_ends_at) {
            return 0;
        }
        
        $remaining = strtotime($this->trial_ends_at) - time();
        return max(0, ceil($remaining / (24 * 60 * 60)));
    }

    /**
     * Activate tenant
     */
    public function activate(): bool
    {
        $this->status = self::STATUS_ACTIVE;
        return $this->save();
    }

    /**
     * Suspend tenant
     */
    public function suspend(): bool
    {
        $this->status = self::STATUS_SUSPENDED;
        return $this->save();
    }

    /**
     * Cancel tenant
     */
    public function cancel(): bool
    {
        $this->status = self::STATUS_CANCELLED;
        return $this->save();
    }

    /**
     * Get setting value
     */
    public function getSetting(string $key, $default = null)
    {
        $settings = $this->settings ?? [];
        return $settings[$key] ?? $default;
    }

    /**
     * Set setting value
     */
    public function setSetting(string $key, $value): void
    {
        $settings = $this->settings ?? [];
        $settings[$key] = $value;
        $this->settings = $settings;
    }

    /**
     * Get business hours
     */
    public function getBusinessHours(): array
    {
        return $this->getSetting('business_hours', [
            'Monday' => [['start' => '09:00', 'end' => '17:00']],
            'Tuesday' => [['start' => '09:00', 'end' => '17:00']],
            'Wednesday' => [['start' => '09:00', 'end' => '17:00']],
            'Thursday' => [['start' => '09:00', 'end' => '17:00']],
            'Friday' => [['start' => '09:00', 'end' => '17:00']],
            'Saturday' => [],
            'Sunday' => []
        ]);
    }

    /**
     * Set business hours
     */
    public function setBusinessHours(array $hours): void
    {
        $this->setSetting('business_hours', $hours);
    }

    /**
     * Get email settings
     */
    public function getEmailSettings(): array
    {
        return $this->getSetting('email', [
            'from_email' => '<EMAIL>',
            'from_name' => $this->business_name,
            'smtp_host' => '',
            'smtp_port' => 587,
            'smtp_username' => '',
            'smtp_password' => '',
            'smtp_encryption' => 'tls'
        ]);
    }

    /**
     * Set email settings
     */
    public function setEmailSettings(array $settings): void
    {
        $this->setSetting('email', $settings);
    }

    /**
     * Get plan limits
     */
    public function getPlanLimits(): array
    {
        $limits = [
            self::PLAN_STARTER => [
                'max_employees' => 2,
                'max_services' => 10,
                'max_reservations_per_month' => 100,
                'features' => ['basic_booking', 'email_notifications']
            ],
            self::PLAN_PROFESSIONAL => [
                'max_employees' => 10,
                'max_services' => 50,
                'max_reservations_per_month' => 500,
                'features' => ['basic_booking', 'email_notifications', 'sms_notifications', 'reports']
            ],
            self::PLAN_PREMIUM => [
                'max_employees' => -1, // unlimited
                'max_services' => -1, // unlimited
                'max_reservations_per_month' => -1, // unlimited
                'features' => ['basic_booking', 'email_notifications', 'sms_notifications', 'reports', 'api_access', 'custom_branding']
            ]
        ];

        return $limits[$this->plan] ?? $limits[self::PLAN_STARTER];
    }

    /**
     * Check if tenant can create more employees
     */
    public function canCreateEmployee(): bool
    {
        $limits = $this->getPlanLimits();
        if ($limits['max_employees'] === -1) {
            return true;
        }

        $currentCount = $this->getEmployeeCount();
        return $currentCount < $limits['max_employees'];
    }

    /**
     * Check if tenant can create more services
     */
    public function canCreateService(): bool
    {
        $limits = $this->getPlanLimits();
        if ($limits['max_services'] === -1) {
            return true;
        }

        $currentCount = $this->getServiceCount();
        return $currentCount < $limits['max_services'];
    }

    /**
     * Get employee count for this tenant
     */
    private function getEmployeeCount(): int
    {
        $instance = new Employee();
        return $instance->newQuery()
            ->where('tenant_id', '=', $this->id)
            ->count();
    }

    /**
     * Get service count for this tenant
     */
    private function getServiceCount(): int
    {
        $instance = new Service();
        return $instance->newQuery()
            ->where('tenant_id', '=', $this->id)
            ->count();
    }

    /**
     * Get tenant statistics
     */
    public function getStatistics(): array
    {
        $customerInstance = new Customer();
        $reservationInstance = new Reservation();
        $employeeInstance = new Employee();
        $serviceInstance = new Service();

        $customerCount = $customerInstance->newQuery()
            ->where('tenant_id', '=', $this->id)
            ->count();

        $reservationCount = $reservationInstance->newQuery()
            ->where('tenant_id', '=', $this->id)
            ->count();

        $employeeCount = $employeeInstance->newQuery()
            ->where('tenant_id', '=', $this->id)
            ->count();

        $serviceCount = $serviceInstance->newQuery()
            ->where('tenant_id', '=', $this->id)
            ->count();

        // Get revenue for current month
        $startOfMonth = date('Y-m-01');
        $endOfMonth = date('Y-m-t');
        
        $monthlyRevenue = $reservationInstance->newQuery()
            ->where('tenant_id', '=', $this->id)
            ->where('date', '>=', $startOfMonth)
            ->where('date', '<=', $endOfMonth)
            ->where('status', '=', Reservation::STATUS_COMPLETED)
            ->get();

        $revenue = array_sum(array_column($monthlyRevenue, 'price'));

        return [
            'customers' => $customerCount,
            'reservations' => $reservationCount,
            'employees' => $employeeCount,
            'services' => $serviceCount,
            'monthly_revenue' => $revenue
        ];
    }

    /**
     * Create default tenant
     */
    public static function createDefault(): self
    {
        $tenant = new static([
            'business_name' => 'Realma Spa',
            'subdomain' => 'realma',
            'plan' => self::PLAN_PREMIUM,
            'status' => self::STATUS_ACTIVE,
            'settings' => [
                'site_name' => 'Realma Spa',
                'email' => [
                    'from_email' => '<EMAIL>',
                    'from_name' => 'Realma Spa'
                ]
            ]
        ]);

        $tenant->save();
        return $tenant;
    }

    /**
     * Validate tenant data
     */
    public static function validate(array $data): array
    {
        $errors = [];

        if (empty($data['business_name'])) {
            $errors['business_name'] = 'Business name is required';
        }

        if (empty($data['subdomain'])) {
            $errors['subdomain'] = 'Subdomain is required';
        } elseif (!preg_match('/^[a-z0-9-]+$/', $data['subdomain'])) {
            $errors['subdomain'] = 'Subdomain can only contain lowercase letters, numbers, and hyphens';
        }

        if (!empty($data['billing_email']) && !filter_var($data['billing_email'], FILTER_VALIDATE_EMAIL)) {
            $errors['billing_email'] = 'Valid billing email is required';
        }

        return $errors;
    }

    /**
     * Before save hook
     */
    public function save(): bool
    {
        // Set trial end date for new trial tenants
        if (!$this->exists && $this->status === self::STATUS_TRIAL && !$this->trial_ends_at) {
            $this->trial_ends_at = date('Y-m-d H:i:s', strtotime('+14 days'));
        }

        return parent::save();
    }
}
