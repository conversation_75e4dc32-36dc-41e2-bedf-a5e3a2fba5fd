<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-calendar-plus me-2"></i>
                    Κλείσε το Ραντεβού σου
                </h4>
            </div>
            <div class="card-body">
                <?php if (empty($services)): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Δεν υπάρχουν διαθέσιμες υπηρεσίες αυτή τη στιγμή.
                    </div>
                <?php else: ?>
                    <form id="bookingForm">
                        <!-- Service Selection -->
                        <div class="mb-4">
                            <label for="service" class="form-label">
                                <i class="fas fa-cut me-2"></i>
                                Επιλέξτε Υπηρεσία *
                            </label>
                            <select class="form-select" id="service" name="service_id" required>
                                <option value="">-- Επιλέξτε Υπηρεσία --</option>
                                <?php foreach ($services as $service): ?>
                                    <option value="<?= $service['id'] ?>" 
                                            data-duration="<?= $service['duration'] ?>" 
                                            data-price="<?= $service['price'] ?>">
                                        <?= htmlspecialchars($service['name']) ?> 
                                        (<?= $service['duration'] ?>λ - €<?= number_format($service['price'], 2) ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- Employee Selection -->
                        <div class="mb-4" id="employeeSection" style="display: none;">
                            <label for="employee" class="form-label">
                                <i class="fas fa-user me-2"></i>
                                Επιλέξτε Εργαζόμενο
                            </label>
                            <select class="form-select" id="employee" name="employee_id">
                                <option value="">-- Επιλέξτε Εργαζόμενο --</option>
                            </select>
                        </div>

                        <!-- Date Selection -->
                        <div class="mb-4">
                            <label for="date" class="form-label">
                                <i class="fas fa-calendar me-2"></i>
                                Επιλέξτε Ημερομηνία *
                            </label>
                            <input type="date" class="form-control" id="date" name="date" 
                                   min="<?= date('Y-m-d') ?>" required>
                        </div>

                        <!-- Time Slots -->
                        <div class="mb-4" id="timeSlotsSection" style="display: none;">
                            <label class="form-label">
                                <i class="fas fa-clock me-2"></i>
                                Επιλέξτε Ώρα *
                            </label>
                            <div id="timeSlots" class="row g-2">
                                <!-- Time slots will be loaded here -->
                            </div>
                        </div>

                        <!-- Customer Information -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="customerName" class="form-label">
                                    <i class="fas fa-user me-2"></i>
                                    Όνομα *
                                </label>
                                <input type="text" class="form-control" id="customerName" 
                                       name="customer_name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="customerPhone" class="form-label">
                                    <i class="fas fa-phone me-2"></i>
                                    Τηλέφωνο *
                                </label>
                                <input type="tel" class="form-control" id="customerPhone" 
                                       name="customer_phone" required>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="customerEmail" class="form-label">
                                <i class="fas fa-envelope me-2"></i>
                                Email (προαιρετικό)
                            </label>
                            <input type="email" class="form-control" id="customerEmail" 
                                   name="customer_email">
                        </div>

                        <div class="mb-4">
                            <label for="notes" class="form-label">
                                <i class="fas fa-comment me-2"></i>
                                Σημειώσεις (προαιρετικό)
                            </label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-check me-2"></i>
                                Κλείσε Ραντεβού
                            </button>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const serviceSelect = document.getElementById('service');
    const employeeSection = document.getElementById('employeeSection');
    const employeeSelect = document.getElementById('employee');
    const dateInput = document.getElementById('date');
    const timeSlotsSection = document.getElementById('timeSlotsSection');
    const timeSlotsContainer = document.getElementById('timeSlots');
    const bookingForm = document.getElementById('bookingForm');

    // Service selection change
    serviceSelect.addEventListener('change', function() {
        const serviceId = this.value;
        if (serviceId) {
            loadEmployees(serviceId);
            employeeSection.style.display = 'block';
        } else {
            employeeSection.style.display = 'none';
            timeSlotsSection.style.display = 'none';
        }
    });

    // Date change
    dateInput.addEventListener('change', function() {
        const serviceId = serviceSelect.value;
        const employeeId = employeeSelect.value;
        const date = this.value;
        
        if (serviceId && date) {
            loadTimeSlots(serviceId, employeeId, date);
        }
    });

    // Employee change
    employeeSelect.addEventListener('change', function() {
        const serviceId = serviceSelect.value;
        const date = dateInput.value;
        
        if (serviceId && date) {
            loadTimeSlots(serviceId, this.value, date);
        }
    });

    function loadEmployees(serviceId) {
        fetch(`/services/${serviceId}/employees`)
            .then(response => response.json())
            .then(data => {
                employeeSelect.innerHTML = '<option value="">-- Οποιοσδήποτε Διαθέσιμος --</option>';
                data.forEach(employee => {
                    employeeSelect.innerHTML += `<option value="${employee.id}">${employee.name}</option>`;
                });
            })
            .catch(error => console.error('Error loading employees:', error));
    }

    function loadTimeSlots(serviceId, employeeId, date) {
        const params = new URLSearchParams({
            service_id: serviceId,
            date: date
        });
        
        if (employeeId) {
            params.append('employee_id', employeeId);
        }

        fetch(`/available-slots?${params}`)
            .then(response => response.json())
            .then(data => {
                timeSlotsContainer.innerHTML = '';
                
                if (data.length === 0) {
                    timeSlotsContainer.innerHTML = '<div class="col-12"><div class="alert alert-warning">Δεν υπάρχουν διαθέσιμες ώρες για την επιλεγμένη ημερομηνία.</div></div>';
                } else {
                    data.forEach(slot => {
                        timeSlotsContainer.innerHTML += `
                            <div class="col-6 col-md-4 col-lg-3">
                                <input type="radio" class="btn-check" name="time_slot" id="slot_${slot.time}" value="${slot.time}" required>
                                <label class="btn btn-outline-primary w-100" for="slot_${slot.time}">
                                    ${slot.time}
                                </label>
                            </div>
                        `;
                    });
                }
                
                timeSlotsSection.style.display = 'block';
            })
            .catch(error => {
                console.error('Error loading time slots:', error);
                timeSlotsContainer.innerHTML = '<div class="col-12"><div class="alert alert-danger">Σφάλμα κατά τη φόρτωση των διαθέσιμων ωρών.</div></div>';
                timeSlotsSection.style.display = 'block';
            });
    }

    // Form submission
    bookingForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const data = Object.fromEntries(formData);
        
        fetch('/book', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Το ραντεβού σας κλείστηκε επιτυχώς!');
                window.location.href = `/booking/${data.reservation_id}`;
            } else {
                alert('Σφάλμα: ' + (data.message || 'Δεν ήταν δυνατή η κλείσιμο του ραντεβού.'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Σφάλμα κατά την υποβολή του ραντεβού.');
        });
    });
});
</script>
