<?php

namespace App\Core\Database;

use App\Core\Config\ConfigManager;
use SQLite3;
use Exception;

/**
 * Database Manager
 * 
 * Handles database connections and operations
 */
class DatabaseManager
{
    private static ?DatabaseManager $instance = null;
    private ?SQLite3 $connection = null;
    private ConfigManager $config;

    public function __construct(ConfigManager $config)
    {
        $this->config = $config;
    }

    /**
     * Get database connection
     */
    public function getConnection(): SQLite3
    {
        if ($this->connection === null) {
            $this->connect();
        }
        return $this->connection;
    }

    /**
     * Connect to database
     */
    private function connect(): void
    {
        $dbPath = $this->config->get('database.path');
        $isNew = !file_exists($dbPath);

        try {
            // Ensure directory exists
            $dataDir = dirname($dbPath);
            if (!is_dir($dataDir)) {
                if (!mkdir($dataDir, 0755, true)) {
                    throw new Exception("Cannot create data directory: $dataDir");
                }
            }

            if (!is_writable($dataDir)) {
                throw new Exception("Data directory is not writable: $dataDir");
            }

            $this->connection = new SQLite3($dbPath);
            $this->connection->enableExceptions(true);

            // Enable WAL mode for better performance
            $this->connection->exec('PRAGMA journal_mode=WAL;');
            $this->connection->exec('PRAGMA synchronous=NORMAL;');
            $this->connection->exec('PRAGMA cache_size=10000;');
            $this->connection->exec('PRAGMA temp_store=MEMORY;');

            // Initialize tables if new database
            if ($isNew) {
                $this->initializeTables();
            }

        } catch (Exception $e) {
            throw new Exception("Database connection failed: " . $e->getMessage());
        }
    }

    /**
     * Initialize database tables
     */
    private function initializeTables(): void
    {
        $this->createTenantsTable();
        $this->createCustomersTable();
        $this->createServicesTable();
        $this->createEmployeesTable();
        $this->createEmployeeServicesTable();
        $this->createReservationsTable();
        $this->createVerificationCodesTable();
        $this->createDefaultTenant();
    }

    /**
     * Create tenants table
     */
    private function createTenantsTable(): void
    {
        $sql = "CREATE TABLE IF NOT EXISTS tenants (
            id TEXT PRIMARY KEY,
            business_name TEXT NOT NULL,
            domain TEXT UNIQUE,
            subdomain TEXT UNIQUE,
            plan TEXT NOT NULL DEFAULT 'starter',
            status TEXT NOT NULL DEFAULT 'trial',
            trial_ends_at TEXT,
            billing_email TEXT,
            settings TEXT DEFAULT '{}',
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL
        )";
        $this->connection->exec($sql);
    }

    /**
     * Create customers table
     */
    private function createCustomersTable(): void
    {
        $sql = "CREATE TABLE customers (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            email TEXT NOT NULL,
            mobile TEXT,
            address TEXT,
            date_of_birth TEXT,
            notes TEXT,
            preferred_contact TEXT DEFAULT 'email',
            preferred_language TEXT DEFAULT 'el',
            total_reservations INTEGER DEFAULT 0,
            total_spent REAL DEFAULT 0,
            last_visit TEXT,
            created_at TEXT NOT NULL,
            user_hash TEXT NOT NULL,
            avatar_url TEXT,
            tenant_id TEXT NOT NULL,
            UNIQUE(email, tenant_id)
        )";
        $this->connection->exec($sql);
    }

    /**
     * Create services table
     */
    private function createServicesTable(): void
    {
        $sql = "CREATE TABLE services (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            duration INTEGER NOT NULL,
            price REAL NOT NULL,
            description TEXT,
            allow_employee_selection BOOLEAN DEFAULT FALSE,
            tenant_id TEXT NOT NULL DEFAULT 'default'
        )";
        $this->connection->exec($sql);
    }

    /**
     * Create employees table
     */
    private function createEmployeesTable(): void
    {
        $sql = "CREATE TABLE employees (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            email TEXT,
            phone TEXT,
            working_hours TEXT,
            status TEXT DEFAULT 'active',
            created_at TEXT NOT NULL,
            tenant_id TEXT NOT NULL DEFAULT 'default'
        )";
        $this->connection->exec($sql);
    }

    /**
     * Create employee_services junction table
     */
    private function createEmployeeServicesTable(): void
    {
        $sql = "CREATE TABLE employee_services (
            employee_id TEXT NOT NULL,
            service_id TEXT NOT NULL,
            PRIMARY KEY (employee_id, service_id),
            FOREIGN KEY (employee_id) REFERENCES employees(id),
            FOREIGN KEY (service_id) REFERENCES services(id)
        )";
        $this->connection->exec($sql);
    }

    /**
     * Create reservations table
     */
    private function createReservationsTable(): void
    {
        $sql = "CREATE TABLE reservations (
            id TEXT PRIMARY KEY,
            customer_id TEXT NOT NULL,
            service TEXT NOT NULL,
            date TEXT NOT NULL,
            time TEXT NOT NULL,
            duration INTEGER NOT NULL,
            price REAL NOT NULL,
            status TEXT NOT NULL,
            employee_id TEXT,
            created_at TEXT NOT NULL,
            tenant_id TEXT NOT NULL DEFAULT 'default',
            FOREIGN KEY (customer_id) REFERENCES customers(id),
            FOREIGN KEY (employee_id) REFERENCES employees(id)
        )";
        $this->connection->exec($sql);
    }

    /**
     * Create verification codes table
     */
    private function createVerificationCodesTable(): void
    {
        $sql = "CREATE TABLE verification_codes (
            email TEXT NOT NULL,
            code TEXT NOT NULL,
            timestamp INTEGER NOT NULL,
            PRIMARY KEY (email, code)
        )";
        $this->connection->exec($sql);
    }

    /**
     * Create default tenant
     */
    private function createDefaultTenant(): void
    {
        $now = date('Y-m-d H:i:s');
        $stmt = $this->connection->prepare("INSERT OR IGNORE INTO tenants 
            (id, business_name, subdomain, plan, status, settings, created_at, updated_at) 
            VALUES ('realma', 'Realma Spa', 'realma', 'premium', 'active', '{}', ?, ?)");
        $stmt->bindValue(1, $now);
        $stmt->bindValue(2, $now);
        $stmt->execute();
    }

    /**
     * Close database connection
     */
    public function close(): void
    {
        if ($this->connection) {
            $this->connection->close();
            $this->connection = null;
        }
    }

    /**
     * Begin transaction
     */
    public function beginTransaction(): void
    {
        $this->getConnection()->exec('BEGIN TRANSACTION');
    }

    /**
     * Commit transaction
     */
    public function commit(): void
    {
        $this->getConnection()->exec('COMMIT');
    }

    /**
     * Rollback transaction
     */
    public function rollback(): void
    {
        $this->getConnection()->exec('ROLLBACK');
    }
}
