<?php

/**
 * Application Entry Point
 * 
 * This is the main entry point for the booking system.
 * All requests are routed through this file.
 */

// Set error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application root
define('APP_ROOT', dirname(__DIR__));

// Autoloader
require_once APP_ROOT . '/vendor/autoload.php';

// Start the application
try {
    // Debug: Check if autoloader works
    if (!class_exists('\App\Core\Application')) {
        throw new Exception('Application class not found. Autoloader issue.');
    }

    $app = \App\Core\Application::getInstance();
    $app->initialize();
    $app->run();
} catch (Exception $e) {
    // Handle fatal errors
    http_response_code(500);

    // Always show error details for debugging
    header('Content-Type: text/html');
    echo "<h1>Application Error</h1>";
    echo "<p><strong>Message:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "<h2>Stack Trace:</h2>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";

    exit(1);
} catch (Error $e) {
    // Handle fatal PHP errors
    http_response_code(500);
    header('Content-Type: text/html');
    echo "<h1>PHP Fatal Error</h1>";
    echo "<p><strong>Message:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "<h2>Stack Trace:</h2>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";

    exit(1);
}
