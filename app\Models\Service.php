<?php

namespace App\Models;

use App\Core\Model\BaseModel;

/**
 * Service Model
 * 
 * Represents a service offered by the business
 */
class Service extends BaseModel
{
    protected string $table = 'services';
    protected bool $timestamps = false;

    protected array $fillable = [
        'name',
        'duration',
        'price',
        'description',
        'allow_employee_selection'
    ];

    protected array $casts = [
        'duration' => 'int',
        'price' => 'float',
        'allow_employee_selection' => 'bool'
    ];

    /**
     * Generate service ID
     */
    protected function generateId(): string
    {
        return 'SRV' . strtoupper(uniqid());
    }

    /**
     * Find service by name
     */
    public static function findByName(string $name): ?self
    {
        $instance = new static();
        $result = $instance->newQuery()
            ->where('name', '=', $name)
            ->first();

        if ($result) {
            $model = new static($result);
            $model->exists = true;
            $model->syncOriginal();
            return $model;
        }

        return null;
    }

    /**
     * Get employees assigned to this service
     */
    public function employees(): array
    {
        $instance = new Employee();
        $results = $instance->newQuery()
            ->join('employee_services', 'employees.id', '=', 'employee_services.employee_id')
            ->where('employee_services.service_id', '=', $this->id)
            ->where('employees.status', '=', 'active')
            ->select(['employees.*'])
            ->get();

        $employees = [];
        foreach ($results as $result) {
            $model = new Employee($result);
            $model->exists = true;
            $model->syncOriginal();
            $employees[] = $model;
        }

        return $employees;
    }

    /**
     * Get available employees for this service
     */
    public function getAvailableEmployees(): array
    {
        return $this->employees();
    }

    /**
     * Check if service allows employee selection
     */
    public function allowsEmployeeSelection(): bool
    {
        return $this->allow_employee_selection === true;
    }

    /**
     * Get formatted duration
     */
    public function getFormattedDuration(): string
    {
        $hours = floor($this->duration / 60);
        $minutes = $this->duration % 60;

        if ($hours > 0 && $minutes > 0) {
            return "{$hours}h {$minutes}m";
        } elseif ($hours > 0) {
            return "{$hours}h";
        } else {
            return "{$minutes}m";
        }
    }

    /**
     * Get formatted price
     */
    public function getFormattedPrice(): string
    {
        return '€' . number_format($this->price, 2);
    }

    /**
     * Get service reservations
     */
    public function reservations(): array
    {
        $instance = new Reservation();
        $results = $instance->newQuery()
            ->where('service', '=', $this->name)
            ->orderBy('date', 'DESC')
            ->get();

        $reservations = [];
        foreach ($results as $result) {
            $model = new Reservation($result);
            $model->exists = true;
            $model->syncOriginal();
            $reservations[] = $model;
        }

        return $reservations;
    }

    /**
     * Get service statistics
     */
    public function getStatistics(): array
    {
        $reservations = $this->reservations();
        $completed = array_filter($reservations, function ($r) {
            return $r->status === Reservation::STATUS_COMPLETED;
        });

        $totalRevenue = array_sum(array_map(function ($r) {
            return $r->price;
        }, $completed));

        return [
            'total_bookings' => count($reservations),
            'completed_bookings' => count($completed),
            'total_revenue' => $totalRevenue,
            'average_price' => count($completed) > 0 ? $totalRevenue / count($completed) : 0
        ];
    }

    /**
     * Get popular services
     */
    public static function getPopular(int $limit = 5): array
    {
        $instance = new static();
        $services = $instance->newQuery()->get();

        $serviceStats = [];
        foreach ($services as $serviceData) {
            $service = new static($serviceData);
            $service->exists = true;
            $service->syncOriginal();

            $stats = $service->getStatistics();
            $serviceStats[] = [
                'service' => $service,
                'bookings' => $stats['total_bookings']
            ];
        }

        // Sort by bookings count
        usort($serviceStats, function ($a, $b) {
            return $b['bookings'] - $a['bookings'];
        });

        return array_slice(array_column($serviceStats, 'service'), 0, $limit);
    }

    /**
     * Search services
     */
    public static function search(string $term = '', int $limit = 20, int $offset = 0): array
    {
        $instance = new static();
        $query = $instance->newQuery();

        if (!empty($term)) {
            $query->where('name', 'LIKE', "%$term%")
                ->orWhere('description', 'LIKE', "%$term%");
        }

        $results = $query->orderBy('name')
            ->limit($limit)
            ->offset($offset)
            ->get();

        $services = [];
        foreach ($results as $result) {
            $model = new static($result);
            $model->exists = true;
            $model->syncOriginal();
            $services[] = $model;
        }

        return $services;
    }

    /**
     * Get search count
     */
    public static function searchCount(string $term = ''): int
    {
        $instance = new static();
        $query = $instance->newQuery();

        if (!empty($term)) {
            $query->where('name', 'LIKE', "%$term%")
                ->orWhere('description', 'LIKE', "%$term%");
        }

        return $query->count();
    }

    /**
     * Assign employee to service
     */
    public function assignEmployee(string $employeeId): bool
    {
        try {
            $conn = $this->newQuery()->getConnection();
            $stmt = $conn->prepare("INSERT OR IGNORE INTO employee_services (employee_id, service_id) VALUES (?, ?)");
            $stmt->bindValue(1, $employeeId);
            $stmt->bindValue(2, $this->id);
            return $stmt->execute() !== false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Remove employee from service
     */
    public function removeEmployee(string $employeeId): bool
    {
        try {
            $conn = $this->newQuery()->getConnection();
            $stmt = $conn->prepare("DELETE FROM employee_services WHERE employee_id = ? AND service_id = ?");
            $stmt->bindValue(1, $employeeId);
            $stmt->bindValue(2, $this->id);
            return $stmt->execute() !== false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Sync employees with service
     */
    public function syncEmployees(array $employeeIds): bool
    {
        try {
            $conn = $this->newQuery()->getConnection();

            // Start transaction
            $conn->exec('BEGIN TRANSACTION');

            // Remove all existing assignments
            $stmt = $conn->prepare("DELETE FROM employee_services WHERE service_id = ?");
            $stmt->bindValue(1, $this->id);
            $stmt->execute();

            // Add new assignments
            foreach ($employeeIds as $employeeId) {
                $stmt = $conn->prepare("INSERT INTO employee_services (employee_id, service_id) VALUES (?, ?)");
                $stmt->bindValue(1, $employeeId);
                $stmt->bindValue(2, $this->id);
                $stmt->execute();
            }

            $conn->exec('COMMIT');
            return true;
        } catch (\Exception $e) {
            $conn->exec('ROLLBACK');
            return false;
        }
    }

    /**
     * Get service duration in hours and minutes
     */
    public function getDurationParts(): array
    {
        $hours = floor($this->duration / 60);
        $minutes = $this->duration % 60;

        return [
            'hours' => $hours,
            'minutes' => $minutes
        ];
    }

    /**
     * Validate service data
     */
    public static function validate(array $data): array
    {
        $errors = [];

        if (empty($data['name'])) {
            $errors['name'] = 'Service name is required';
        }

        if (empty($data['duration']) || !is_numeric($data['duration']) || $data['duration'] <= 0) {
            $errors['duration'] = 'Valid duration is required';
        }

        if (empty($data['price']) || !is_numeric($data['price']) || $data['price'] <= 0) {
            $errors['price'] = 'Valid price is required';
        }

        return $errors;
    }

    /**
     * Get all services as options array
     */
    public static function getOptions(): array
    {
        $services = static::all();
        $options = [];

        foreach ($services as $service) {
            $options[$service->name] = $service->name . ' (' . $service->getFormattedDuration() . ' - ' . $service->getFormattedPrice() . ')';
        }

        return $options;
    }

    /**
     * Before delete - check for dependencies
     */
    public function delete(): bool
    {
        // Check if service has reservations
        $reservations = $this->reservations();
        if (!empty($reservations)) {
            throw new \Exception('Cannot delete service with existing reservations');
        }

        // Remove employee assignments
        try {
            $conn = $this->newQuery()->getConnection();
            $stmt = $conn->prepare("DELETE FROM employee_services WHERE service_id = ?");
            $stmt->bindValue(1, $this->id);
            $stmt->execute();
        } catch (\Exception $e) {
            // Continue with deletion
        }

        return parent::delete();
    }
}
