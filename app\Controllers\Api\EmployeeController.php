<?php

namespace App\Controllers\Api;

use App\Controllers\BaseController;
use App\Core\Http\Request;
use App\Core\Http\Response;
use App\Models\Employee;
use App\Models\Service;

/**
 * Employee API Controller
 * 
 * Handles employee-related API endpoints
 */
class EmployeeController extends BaseController
{
    /**
     * Get all employees
     */
    public function index(Request $request): Response
    {
        try {
            $pagination = $this->getPaginationParams($request);
            $search = $request->input('search', '');
            
            $employees = Employee::search($search, $pagination['limit'], $pagination['offset']);
            $total = Employee::searchCount($search);
            
            // Enrich with service data
            foreach ($employees as &$employee) {
                $services = $employee->services();
                $employee = $this->formatModel($employee);
                $employee['services'] = $this->formatCollection($services);
                $employee['service_count'] = count($services);
            }
            
            return $this->paginatedResponse(
                $employees,
                $total,
                $pagination['page'],
                $pagination['limit']
            );
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get employee by ID
     */
    public function show(Request $request, array $params): Response
    {
        try {
            $employee = Employee::find($params['id']);
            
            if (!$employee) {
                return $this->notFound('Employee not found');
            }
            
            $data = $this->formatModel($employee);
            $data['services'] = $this->formatCollection($employee->services());
            $data['statistics'] = $employee->getStatistics();
            
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Create new employee
     */
    public function store(Request $request): Response
    {
        try {
            $data = $this->validate($request, [
                'name' => 'required',
                'email' => 'email'
            ]);

            // Validate employee data
            $errors = Employee::validate($data);
            if (!empty($errors)) {
                return $this->validationError($errors);
            }

            $data['status'] = Employee::STATUS_ACTIVE;
            $employee = Employee::create($data);
            
            return $this->created($employee, 'Employee created successfully');
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Update employee
     */
    public function update(Request $request, array $params): Response
    {
        try {
            $employee = Employee::find($params['id']);
            
            if (!$employee) {
                return $this->notFound('Employee not found');
            }

            $data = $request->only(['name', 'email', 'phone', 'working_hours', 'status']);

            // Validate employee data
            $errors = Employee::validate($data);
            if (!empty($errors)) {
                return $this->validationError($errors);
            }

            foreach ($data as $key => $value) {
                if ($value !== null) {
                    $employee->$key = $value;
                }
            }

            $employee->save();
            
            return $this->updated($employee, 'Employee updated successfully');
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Delete employee
     */
    public function destroy(Request $request, array $params): Response
    {
        try {
            $employee = Employee::find($params['id']);
            
            if (!$employee) {
                return $this->notFound('Employee not found');
            }

            try {
                $employee->delete();
                return $this->deleted('Employee deleted successfully');
            } catch (\Exception $e) {
                return $this->error($e->getMessage(), 409);
            }
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Activate employee
     */
    public function activate(Request $request, array $params): Response
    {
        try {
            $employee = Employee::find($params['id']);
            
            if (!$employee) {
                return $this->notFound('Employee not found');
            }

            $employee->activate();
            
            return $this->success($employee, 'Employee activated successfully');
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Deactivate employee
     */
    public function deactivate(Request $request, array $params): Response
    {
        try {
            $employee = Employee::find($params['id']);
            
            if (!$employee) {
                return $this->notFound('Employee not found');
            }

            $employee->deactivate();
            
            return $this->success($employee, 'Employee deactivated successfully');
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get employee services
     */
    public function services(Request $request, array $params): Response
    {
        try {
            $employee = Employee::find($params['id']);
            
            if (!$employee) {
                return $this->notFound('Employee not found');
            }

            $services = $employee->services();
            
            return $this->success($this->formatCollection($services));
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Assign services to employee
     */
    public function assignServices(Request $request, array $params): Response
    {
        try {
            $employee = Employee::find($params['id']);
            
            if (!$employee) {
                return $this->notFound('Employee not found');
            }

            $data = $this->validate($request, [
                'service_ids' => 'required'
            ]);

            $serviceIds = is_array($data['service_ids']) ? $data['service_ids'] : [$data['service_ids']];

            // Validate all services exist
            foreach ($serviceIds as $serviceId) {
                $service = Service::find($serviceId);
                if (!$service) {
                    return $this->error("Service $serviceId not found", 404);
                }
            }

            $employee->syncServices($serviceIds);
            
            return $this->success(null, 'Services assigned to employee successfully');
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Remove service from employee
     */
    public function removeService(Request $request, array $params): Response
    {
        try {
            $employee = Employee::find($params['id']);
            
            if (!$employee) {
                return $this->notFound('Employee not found');
            }

            $serviceId = $params['service_id'];
            $service = Service::find($serviceId);
            
            if (!$service) {
                return $this->notFound('Service not found');
            }

            $employee->removeService($serviceId);
            
            return $this->success(null, 'Service removed from employee successfully');
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get employee working hours
     */
    public function workingHours(Request $request, array $params): Response
    {
        try {
            $employee = Employee::find($params['id']);
            
            if (!$employee) {
                return $this->notFound('Employee not found');
            }

            $workingHours = $employee->working_hours ?? Employee::getDefaultWorkingHours();
            
            return $this->success($workingHours);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Update employee working hours
     */
    public function updateWorkingHours(Request $request, array $params): Response
    {
        try {
            $employee = Employee::find($params['id']);
            
            if (!$employee) {
                return $this->notFound('Employee not found');
            }

            $data = $this->validate($request, [
                'working_hours' => 'required'
            ]);

            $employee->working_hours = $data['working_hours'];
            $employee->save();
            
            return $this->success($employee, 'Working hours updated successfully');
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get employee availability for date
     */
    public function availability(Request $request, array $params): Response
    {
        try {
            $employee = Employee::find($params['id']);
            
            if (!$employee) {
                return $this->notFound('Employee not found');
            }

            $date = $request->input('date', date('Y-m-d'));
            
            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
                return $this->error('Invalid date format. Use YYYY-MM-DD', 400);
            }

            $availability = $employee->getAvailabilityForDate($date);
            
            return $this->success([
                'date' => $date,
                'available_times' => $availability
            ]);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get employee reservations
     */
    public function reservations(Request $request, array $params): Response
    {
        try {
            $employee = Employee::find($params['id']);
            
            if (!$employee) {
                return $this->notFound('Employee not found');
            }

            $reservations = $employee->reservations();
            
            return $this->success($this->formatCollection($reservations));
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get employee statistics
     */
    public function statistics(Request $request, array $params): Response
    {
        try {
            $employee = Employee::find($params['id']);
            
            if (!$employee) {
                return $this->notFound('Employee not found');
            }

            $stats = $employee->getStatistics();
            
            return $this->success($stats);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get active employees
     */
    public function active(Request $request): Response
    {
        try {
            $employees = Employee::getActive();
            
            return $this->success($this->formatCollection($employees));
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get employees for specific service
     */
    public function forService(Request $request, array $params): Response
    {
        try {
            $serviceId = $params['service_id'];
            $service = Service::find($serviceId);
            
            if (!$service) {
                return $this->notFound('Service not found');
            }

            $employees = Employee::forService($serviceId);
            
            return $this->success($this->formatCollection($employees));
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }
}
